<script setup lang="ts">
// 不再直接导入组件，使用路由
</script>

<template>
  <!-- 使用 router-view 显示当前路由对应的组件 -->
  <router-view />
</template>

<style scoped>
/* 清除全局外边距和内边距 */
:root {
  margin: 0;
  padding: 0;
}
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
