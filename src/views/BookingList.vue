<template>
    <div class="booking-list-page">
        <!-- 顶部导航 -->
        <van-nav-bar title="定单" left-arrow @click-left="$router.go(-1)" class="custom-nav-bar" />
        <img src="@/assets/images/add.png" class="add-img" @click="goToCreate" />

        <!-- 搜索栏 -->
        <div class="search-section">
            <van-search v-model="searchValue" placeholder="请输入客户姓名或房号" @search="handleSearch" class="custom-search"
                @clear="resetList" />
            <!-- <span class="cancel-btn" @click="handleCancel">取消</span> -->
        </div>

        <!-- 状态标签栏 -->
        <div class="status-tabs">
            <div v-for="(tab, index) in statusTabs" :key="index" class="status-tab"
                :class="{ active: activeTab === index }" @click="handleTabChange(index)">
                <span class="tab-text">{{ tab.label }}</span>
                <!-- <span class="tab-count">{{ tab.count }}</span> -->
            </div>
        </div>

        <!-- 筛选栏 -->
        <div class="filter-section">
            <div class="filter-left">
                <van-dropdown-menu class="filter-dropdown">
                    <van-dropdown-item v-model="sortValue" :options="sortOptions" @change="handleSortChange" />
                    <!-- status -->
                </van-dropdown-menu>
            </div>
            <div class="filter-right">
                <span class="more-filter" @click="showMoreFilter">
                    <span class="more-filter-text">更多筛选</span>
                    <img src="@/assets/images/filter.png" class="more-filter-img" />
                </span>
            </div>
        </div>

        <!-- 列表内容 -->
        <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
            class="list-content"
        >
            <div v-for="item in bookingList" :key="item.id" class="booking-item">
                <!-- 蓝色渐变标题栏 -->
                <!-- <div class="item-header">
                    <span class="item-title">{{ item.title }}</span>
                    <div class="status-badge" :class="getStatusBadgeClass(item.status)">
                        {{ item.status }}
                    </div>
                </div> -->
                <moduleTitle :title="item.roomName" :status="item.statusName" :statusCode="item.status"></moduleTitle>

                <!-- 白色信息区域 -->
                <div class="item-content">
                    <div class="info-section" v-if="activeTab === 0">
                        <div class="info-item">
                            <span class="info-label">客户姓名：</span>
                            <span class="info-value">{{ item.customerName }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">应收定金：</span>
                            <span class="info-value">{{ item.bookingAmount }}元</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">应收日期：</span>
                            <span class="info-value">{{ item.receivableDate }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">创建日期：</span>
                            <span class="info-value">{{ item.createTime }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">创建人：</span>
                            <span class="info-value">{{ item.createByName }}</span>
                        </div>
                    </div>
                    <div class="info-section" v-if="activeTab === 1">
                        <div class="info-item">
                            <span class="info-label">客户姓名：</span>
                            <span class="info-value">{{ item.customerName }}</span>
                        </div>
                        <div class="info-section-double">
                            <div class="info-item">
                                <span class="info-label">应收定金：</span>
                                <span class="info-value">{{ item.bookingAmount }}元</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">实收定金：</span>
                                <span class="info-value">{{ item.receivedAmount }}元</span>
                            </div>
                        </div>
                        <div class="info-section-double">
                            <div class="info-item">
                                <span class="info-label">应收日期：</span>
                                <span class="info-value">{{ item.receivableDate }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">实收日期：</span>
                                <span class="info-value">{{ item.receivedDate }}</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <span class="info-label">创建日期：</span>
                            <span class="info-value">{{ item.createTime }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">创建人：</span>
                            <span class="info-value">{{ item.createByName }}</span>
                        </div>
                    </div>

                    <div class="info-section" v-if="activeTab === 2">
                        <div class="info-item">
                            <span class="info-label">客户姓名：</span>
                            <span class="info-value">{{ item.customerName }}</span>
                        </div>
                        <div class="info-section-double">
                            <div class="info-item">
                                <span class="info-label">应收定金：</span>
                                <span class="info-value">{{ item.bookingAmount }}元</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">实收定金：</span>
                                <span class="info-value">{{ item.receivedAmount }}元</span>
                            </div>
                        </div>
                        <div class="info-section-double">
                            <div class="info-item">
                                <span class="info-label">应收日期：</span>
                                <span class="info-value">{{ item.receivableDate }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">实收日期：</span>
                                <span class="info-value">{{ item.receivedDate }}</span>
                            </div>
                        </div>
                        <div class="info-section-double">
                            <div class="info-item">
                                <span class="info-label">签约日期：</span>
                                <span class="info-value">{{ item.expectSignDate }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">签约人：</span>
                                <span class="info-value">{{ item.signerName }}</span>
                            </div>
                        </div>
                        <div class="info-item">
                            <span class="info-label">签约房源：</span>
                            <span class="info-value">{{ item.roomName }}</span>
                        </div>

                        <!-- <div class="info-item">
                            <span class="info-label">创建日期：</span>
                            <span class="info-value">{{ item.createTime }}</span>
                        </div> -->
                        <div class="info-item">
                            <span class="info-label">创建人：</span>
                            <span class="info-value">{{ item.createByName }}</span>
                        </div>
                    </div>
                    <div class="info-section" v-if="activeTab === 3">
                        <div class="info-item">
                            <span class="info-label">客户姓名：</span>
                            <span class="info-value">{{ item.customerName }}</span>
                        </div>
                        <div class="info-section-double">
                            <div class="info-item">
                                <span class="info-label">应收定金：</span>
                                <span class="info-value">{{ item.bookingAmount }}元</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">实收定金：</span>
                                <span class="info-value">{{ item.receivedAmount }}元</span>
                            </div>
                        </div>
                        <div class="info-section-double">
                            <div class="info-item">
                                <span class="info-label">应收日期：</span>
                                <span class="info-value">{{ item.receivableDate }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">实收日期：</span>
                                <span class="info-value">{{ item.receivedDate }}</span>
                            </div>
                        </div>
                        <div class="info-section-double">
                            <div class="info-item">
                                <span class="info-label">作废日期：</span>
                                <span class="info-value">{{ item.cancelTime }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">作废人：</span>
                                <span class="info-value">{{ item.cancelByName }}</span>
                            </div>
                        </div>
                        <div class="info-item">
                            <span class="info-label">作废原因：</span>
                            <span class="info-value">{{ formatCancelReason(item.cancelReason) }}</span>
                        </div>

                        <!-- <div class="info-item">
                            <span class="info-label">创建日期：</span>
                            <span class="info-value">{{ item.createTime }}</span>
                        </div> -->
                        <div class="info-item">
                            <span class="info-label">创建人：</span>
                            <span class="info-value">{{ item.createByName }}</span>
                        </div>
                    </div>

                    <!-- 右侧操作区域 -->
                    <div class="action-section">
                        <!-- 收款码（如果有） -->
                        <div class="qr-code" @click.stop="showQrCode(item)">
                            <img src="@/assets/images/skm.png" class="qr-code-img" v-if="item.status === 1" />
                            <!-- <van-icon name="qr" size="40" color="#ccc" v-if="item.status === 1"/> -->
                            <!-- <span class="qr-label" v-if="item.status === 1">收款码</span> -->
                        </div>

                        <!-- 退定按钮 -->
                        <van-button type="primary" size="small" round @click.stop="handleRefund(item)"
                            class="refund-btn" v-if="item.status === 2">
                            退定
                        </van-button>
                        <!-- 退定按钮 -->
                        <van-button type="danger" size="small" round @click.stop="handleDelete(item)" class="refund-btn"
                            v-if="item.status === 0">
                            删除
                        </van-button>
                        <!-- 退定按钮 -->
                        <van-button type="warning" size="small" round @click.stop="handleInvalid(item)"
                            class="refund-btn" v-if="item.status === 1">
                            作废
                        </van-button>
                        <!-- 查看按钮 -->
                        <van-button type="primary" size="small" round @click.stop="goToDetail(item)" class="refund-btn"
                            v-if="item.status === 3 || item.status === 4">
                            查看
                        </van-button>
                    </div>
                </div>
            </div>
            <div class="empty-content" v-if="bookingList.length === 0 && !loading">
                <van-empty description="暂无数据" />
            </div>
        </van-list>

        <!-- 分页组件 -->
        <!-- <div class="pagination-wrapper" v-if="totalCount > 0">
            <van-pagination 
                v-model="currentPage" 
                :total-items="totalCount" 
                :items-per-page="pageSize"
                :show-page-size="3"
                @change="handlePageChange"
                mode="simple"
            />
        </div> -->

        <!-- 收款码弹框 -->
        <van-popup v-model:show="showQRCodePopup" position="center" :close-on-click-overlay="true" round
            class="qr-code-popup">
            <div class="qr-code-content">
                <div class="qr-code-header">
                    <h3 class="qr-code-title">收款码</h3>
                    <van-icon name="cross" @click="closeQRCodePopup" class="close-icon" />
                </div>
                <!--                  
                 <div class="booking-info">
                     <div class="info-item">
                         <span class="label">项目名称：</span>
                         <span class="value">{{ currentBookingInfo.projectName }}</span>
                     </div>
                     <div class="info-item">
                         <span class="label">意向房源：</span>
                         <span class="value">{{ currentBookingInfo.roomName }}</span>
                     </div>
                     <div class="info-item">
                         <span class="label">客户名称：</span>
                         <span class="value">{{ currentBookingInfo.customerName }}</span>
                     </div>
                     <div class="info-item">
                         <span class="label">支付金额：</span>
                         <span class="value amount">¥{{ currentBookingInfo.unpaidAmount || currentBookingInfo.bookingAmount }}</span>
                     </div>
                 </div> -->

                <div class="qr-code-section">
                    <!-- <div v-if="qrCodeLoading" class="loading-section">
                         <van-loading size="24px" />
                         <span>生成收款码中...</span>
                     </div> -->
                    <div class="qr-code-wrapper">
                        <QRCode :value="qrCodeData" :size="200" />
                    </div>
                    <!-- <div v-else class="error-section">
                         <span>收款码生成失败</span>
                     </div> -->
                </div>

                <!-- <div class="qr-code-actions">
                     <van-button 
                         type="primary" 
                         @click="regenerateQRCode"
                         :loading="qrCodeLoading"
                         class="action-btn"
                     >
                         重新生成
                     </van-button>
                 </div> -->
            </div>
        </van-popup>

        <!-- 更多筛选弹框 -->
        <van-popup v-model:show="showFilterPopup" position="bottom" :style="{ height: '70%' }" round
            class="filter-popup">
            <div class="filter-content">
                <div class="filter-header">
                    <h3 class="filter-title">更多筛选</h3>
                    <van-icon name="cross" @click="closeFilterPopup" class="close-icon" />
                </div>
                <!-- activeTab === 0 -->
                <!-- status
integer <int32>
状态: 0-草稿,1-待收费,2-已生效,3-已转签,4-已作废 -->
                <!-- 状态：草稿、待收费
应收款日期：区间输入
创建日期：区间输入
创建人：输入模糊查询
查看所有定单：选择后展示所有定单，包含创建人非本人的定单 -->

                <div class="filter-form">

                    <van-cell-group>
                        <!-- 状态 -->
                        <van-field v-model="filterForm.status" readonly name="status" label="状态" placeholder="请选择"
                            input-align="right" @click="showStatusPicker = true" v-if="activeTab === 0">
                            <template #input>{{ statusFormatted(filterForm.status) || '请选择' }}</template>
                            <template #button>
                                <i class="van-icon van-icon-arrow-down"></i>
                            </template>
                        </van-field>



                        <!-- 作废日期 cancelTimeStart cancelTimeEnd -->
                        <!-- <van-cell-group v-if="activeTab === 3"> -->
                        <van-field readonly clickable label="作废日期" v-model="cancelTimeRangeText" placeholder="请选择作废日期区间"
                            @click="showCancelTimePicker = true" input-align="right" v-if="activeTab === 3">
                            <template #button>
                                <i class="van-icon van-icon-arrow-down"></i>
                            </template>
                        </van-field>
                        <!-- </van-cell-group> -->
                        <!-- <van-cell-group v-if="activeTab === 3"> -->
                        <van-field v-model="filterForm.cancelByName" label="作废人" placeholder="请输入作废人姓名" clearable
                            input-align="right" v-if="activeTab === 3" />
                        <!-- </van-cell-group> -->
                        <!-- 签约日期 -->
                        <!-- <van-cell-group v-if="activeTab === 2"> -->
                        <van-field readonly clickable label="签约日期" v-model="signDateRangeText" placeholder="请选择签约日期区间"
                            @click="showSignDatePicker = true" input-align="right" v-if="activeTab === 2">
                            <template #button>
                                <i class="van-icon van-icon-arrow-down"></i>
                            </template>
                        </van-field>
                        <!-- </van-cell-group> -->
                        <!-- 应收款日期：区间输入 receivableDateStart receivableDateEnd -->
                        <!-- <van-cell-group v-if="activeTab === 0 || activeTab === 1"> -->
                        <van-field readonly clickable label="应收款日期" v-model="receivableDateRangeText"
                            placeholder="请选择应收款日期区间" @click="showReceivableDatePicker = true" label-width="100px"
                            input-align="right" v-if="activeTab === 0 || activeTab === 1">
                            <template #button>
                                <i class="van-icon van-icon-arrow-down"></i>
                            </template>
                        </van-field>
                        <!-- </van-cell-group> -->

                        <!-- 实收日期 -->
                        <!-- <van-cell-group v-if="activeTab === 1"> -->
                        <van-field readonly clickable label="实收日期" v-model="actualReceiveTimeRangeText"
                            placeholder="请选择实收日期区间" @click="showActualReceiveTimePicker = true" input-align="right"
                            v-if="activeTab === 1">
                            <template #button>
                                <i class="van-icon van-icon-arrow-down"></i>
                            </template>
                        </van-field>
                        <!-- </van-cell-group> -->

                        <!-- 创建日期 -->
                        <!-- <van-cell-group v-if="activeTab === 0 || activeTab === 1 || activeTab === 2 || activeTab === 3"> -->
                        <van-field readonly clickable label="创建日期" v-model="createTimeRangeText" placeholder="请选择创建日期区间"
                            @click="showCreateTimePicker = true" input-align="right"
                            v-if="activeTab === 0 || activeTab === 1 || activeTab === 2 || activeTab === 3">
                            <template #button>
                                <i class="van-icon van-icon-arrow-down"></i>
                            </template>
                        </van-field>
                        <!-- </van-cell-group> -->


                        <!-- 创建人 -->
                        <!-- <van-cell-group v-if="activeTab === 0 || activeTab === 1 || activeTab === 2 || activeTab === 3"> -->
                        <van-field v-model="filterForm.createByName" label="创建人" placeholder="请输入创建人姓名" clearable
                            input-align="right"
                            v-if="activeTab === 0 || activeTab === 1 || activeTab === 2 || activeTab === 3" />
                        <!-- </van-cell-group> -->




                        <!-- 签约人 -->
                        <!-- <van-cell-group v-if="activeTab === 2"> -->
                        <van-field v-model="filterForm.signByName" label="签约人" placeholder="请输入签约人姓名" clearable
                            input-align="right" v-if="activeTab === 2" />
                        <!-- </van-cell-group> -->

                        <!-- 查看所有定单：选择后展示所有定单，包含创建人非本人的定单 -->
                        <!-- <van-field v-model="filterForm.isAll" label="查看所有定单" placeholder="请选择" clearable input-align="right"  v-if="activeTab === 0"/>
                      -->
                        <van-field name="switch" label="查看所有定单" input-align="right" label-width="160px">
                            <template #input>
                                <van-switch v-model="isAll" />
                            </template>
                        </van-field>

                    </van-cell-group>
                </div>
                <van-popup v-model:show="showStatusPicker" destroy-on-close position="bottom">
                    <van-picker :columns="statusColumns" :model-value="statusValue" @confirm="onStatusConfirm"
                        @cancel="showStatusPicker = false" />
                </van-popup>
                <!-- 应收款日期选择器 -->
                <van-popup v-model:show="showReceivableDatePicker" position="bottom" @confirm="onReceivableDateConfirm"
                    @cancel="onReceivableDateCancel">
                    <van-picker-group title="应收款日期" :tabs="['开始日期', '结束日期']" @confirm="onReceivableDateConfirm"
                        @cancel="onReceivableDateCancel">
                        <van-date-picker v-model="receivableStartDate" :min-date="new Date(2020, 0, 1)"
                            :max="new Date(2030, 11, 31)" />
                        <van-date-picker v-model="receivableEndDate" :min-date="receivableStartMinDate"
                            :max="new Date(2030, 11, 31)" />
                    </van-picker-group>
                </van-popup>


                <!-- 签约日期选择器 -->
                <van-popup v-model:show="showSignDatePicker" position="bottom" @confirm="onSignDateConfirm"
                    @cancel="onSignDateCancel">
                    <van-picker-group title="签约日期" :tabs="['开始日期', '结束日期']" @confirm="onSignDateConfirm"
                        @cancel="onSignDateCancel">
                        <van-date-picker v-model="signStartDate" :min-date="new Date(2020, 0, 1)"
                            :max="new Date(2030, 11, 31)" />
                        <van-date-picker v-model="signEndDate" :min-date="signEndMinDate"
                            :max="new Date(2030, 11, 31)" />
                    </van-picker-group>
                </van-popup>

                <!-- 创建日期选择器 -->
                <van-popup v-model:show="showCreateTimePicker" position="bottom" @confirm="onCreateTimeConfirm"
                    @cancel="onCreateTimeCancel">
                    <van-picker-group title="创建日期" :tabs="['开始日期', '结束日期']" @confirm="onCreateTimeConfirm"
                        @cancel="onCreateTimeCancel">
                        <van-date-picker v-model="createStartDate" :min-date="new Date(2020, 0, 1)"
                            :max="new Date(2030, 11, 31)" />
                        <van-date-picker v-model="createEndDate" :min-date="createEndMinDate"
                            :max="new Date(2030, 11, 31)" />
                    </van-picker-group>
                </van-popup>

                <!-- 实收日期选择器 -->
                <van-popup v-model:show="showActualReceiveTimePicker" position="bottom"
                    @confirm="onActualReceiveTimeConfirm" @cancel="onActualReceiveTimeCancel">
                    <van-picker-group title="实收日期" :tabs="['开始日期', '结束日期']" @confirm="onActualReceiveTimeConfirm"
                        @cancel="onActualReceiveTimeCancel">
                        <van-date-picker v-model="actualReceiveStartDate" :min-date="new Date(2020, 0, 1)"
                            :max="new Date(2030, 11, 31)" />
                        <van-date-picker v-model="actualReceiveEndDate" :min-date="actualReceiveEndMinDate"
                            :max="new Date(2030, 11, 31)" />
                    </van-picker-group>
                </van-popup>

                <!-- 作废日期选择器 -->
                <van-popup v-model:show="showCancelTimePicker" position="bottom" @confirm="onCancelTimeConfirm"
                    @cancel="onCancelTimeCancel">
                    <van-picker-group title="作废日期" :tabs="['开始日期', '结束日期']" @confirm="onCancelTimeConfirm"
                        @cancel="onCancelTimeCancel">
                        <van-date-picker v-model="cancelStartDate" :min-date="new Date(2020, 0, 1)"
                            :max="new Date(2030, 11, 31)" />
                        <van-date-picker v-model="cancelEndDate" :min-date="cancelEndMinDate"
                            :max="new Date(2030, 11, 31)" />
                    </van-picker-group>
                </van-popup>

                <div class="filter-actions">
                    <van-button class="reset-btn" @click="resetFilter">重置</van-button>
                    <van-button type="primary" class="confirm-btn" @click="applyFilter">确定</van-button>
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import moduleTitle from '@/components/ModuleTitle.vue'
import QRCode from '@/components/QRCode.vue'
import { getBookingList, payBooking, type BookingPaymentDto, deleteBooking, invalidBooking, getBookingSummary } from '@/api/booking'
import { showToast, showConfirmDialog } from 'vant'

const router = useRouter()

// 搜索相关
const searchValue = ref('')
const pageNum = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
// 滚动加载相关变量已在上方声明

// 状态标签
const activeTab = ref(0)
const statusTabs = ref([
    { label: '待生效', count: 0, searchType: 1 },
    { label: '已生效', count: 0, searchType: 2 },
    { label: '已转签', count: 0, searchType: 3 },
    { label: '已作废', count: 0, searchType: 4 }
])

// 筛选相关
const sortValue = ref('1')
const sortOptions = ref([
    // orderType：1-创建时间倒叙 2-应收日期倒叙 3-实收日期倒叙 4-签约日期倒叙 5-作废日期倒叙
    { text: '最新创建', value: '1' },
    { text: '最新应收', value: '2' },
    // { text: '实收日期', value: '3' },
    // { text: '签约日期', value: '4' },
    // { text: '作废日期', value: '5' }
])

// 列表数据
const bookingList = ref<any[]>([])
const loading = ref(false)
const finished = ref(false)

// 收款码弹框相关
const showQRCodePopup = ref(false)
const qrCodeData = ref('')
const currentBookingInfo = ref<any>({})
const qrCodeLoading = ref(false)

// 更多筛选弹框相关
const showFilterPopup = ref(false)
const filterForm = ref({
    status: '',           // 状态
    signDateStart: '',    // 签约日期开始
    signDateEnd: '',      // 签约日期结束
    createTimeStart: '',  // 创建日期开始
    createTimeEnd: '',    // 创建日期结束
    createByName: '',     // 创建人
    signByName: '',       // 签约人
    receivableDateStart: '', // 应收款日期开始
    receivableDateEnd: '',    // 应收款日期结束
    actualReceiveTimeStart: '', // 实收日期开始
    actualReceiveTimeEnd: '',    // 实收日期结束
    cancelTimeStart: '',        // 作废日期开始
    cancelTimeEnd: '',          // 作废日期结束
    cancelByName: ''            // 作废人
})
const isAll = ref(false)

let receivableDateRangeText = ref('')

// 日期区间和最小最大值限制
const minDate = new Date(2020, 0, 1)
const maxDate = new Date(2030, 11, 31)

// 日期选择器相关
const showSignDatePicker = ref(false)
const showCreateTimePicker = ref(false)
const showReceivableDatePicker = ref(false)
const showActualReceiveTimePicker = ref(false)
const showCancelTimePicker = ref(false)

// 应收款日期选择
const receivableStartDate = ref(['2024', '01', '01'])
const receivableEndDate = ref(['2024', '12', '31'])
const receivableStartMinDate = computed(() =>
    new Date(
        Number(receivableStartDate.value[0]),
        Number(receivableStartDate.value[1]) - 1,
        Number(receivableStartDate.value[2])
    )
)

// 签约日期选择
const signStartDate = ref(['2024', '01', '01'])
const signEndDate = ref(['2024', '12', '31'])
const signEndMinDate = computed(() =>
    new Date(
        Number(signStartDate.value[0]),
        Number(signStartDate.value[1]) - 1,
        Number(signStartDate.value[2])
    )
)

// 创建日期选择
const createStartDate = ref(['2024', '01', '01'])
const createEndDate = ref(['2024', '12', '31'])
const createEndMinDate = computed(() =>
    new Date(
        Number(createStartDate.value[0]),
        Number(createStartDate.value[1]) - 1,
        Number(createStartDate.value[2])
    )
)

// 实收日期选择
const actualReceiveStartDate = ref(['2024', '01', '01'])
const actualReceiveEndDate = ref(['2024', '12', '31'])
const actualReceiveEndMinDate = computed(() =>
    new Date(
        Number(actualReceiveStartDate.value[0]),
        Number(actualReceiveStartDate.value[1]) - 1,
        Number(actualReceiveStartDate.value[2])
    )
)

// 作废日期选择
const cancelStartDate = ref(['2024', '01', '01'])
const cancelEndDate = ref(['2024', '12', '31'])
const cancelEndMinDate = computed(() =>
    new Date(
        Number(cancelStartDate.value[0]),
        Number(cancelStartDate.value[1]) - 1,
        Number(cancelStartDate.value[2])
    )
)

// 应收款日期选择
const receivableDateStart = ref('')
const receivableDateEnd = ref('')
const receivableEndMinDate = computed(() =>
    new Date(
        Number(receivableDateStart.value[0]),
        Number(receivableDateStart.value[1]) - 1,
        Number(receivableDateStart.value[2])
    )
)

// 计算属性：日期区间显示文本
let signDateRangeText = ref('')

// const signDateRangeText = computed(() => {
//     if (filterForm.value.signDateStart && filterForm.value.signDateEnd) {
//         return `${filterForm.value.signDateStart} 至 ${filterForm.value.signDateEnd}`
//     }
//     return ''
// })

// const createTimeRangeText = computed(() => {
//     if (filterForm.value.createTimeStart && filterForm.value.createTimeEnd) {
//         return `${filterForm.value.createTimeStart} 至 ${filterForm.value.createTimeEnd}`
//     }
//     return ''
// })

let createTimeRangeText = ref('')
let actualReceiveTimeRangeText = ref('')
let cancelTimeRangeText = ref('')

let showStatusPicker = ref(false)
let statusValue = ref([])
let statusColumns = ref([
    { text: '草稿', value: 0 },
    { text: '待收费', value: 1 },
    // { text: '已生效', value: 2 },
    // { text: '已转签', value: 3 },
    // { text: '已作废', value: 4 }
])
// const statusArr = ref([])
const onStatusConfirm = (value: any) => {
    console.log(value)
    filterForm.value.status = value.selectedValues[0]
    showStatusPicker.value = false
}

const statusFormatted = (status: string) => {
    console.log(status)
    if (!status) {
        return ''
    }
    return ['草稿', '待收费', '已生效', '已转签', '已作废'][Number(status)] || ''
}
// 格式化作废原因
const formatCancelReason = (cancelReason: number | string) => {
    const reasonMap: Record<string, string> = {
        '0': '退定退款',
        '1': '退定不退款',
        '2': '未收款取消预定',
        '3': '转签合同作废'
    }
    return reasonMap[String(cancelReason)] || cancelReason
}

// 获取列表数据
const fetchBookingList = async () => {
    let currentProject = localStorage.getItem('currentProject')
    if (currentProject) {
        currentProject = JSON.parse(currentProject)
    } else {

    }
    try {
        loading.value = true
        let params = {
            orderType: sortValue.value,
            pageNum: pageNum.value,
            pageSize: pageSize.value,
            searchType: statusTabs.value[activeTab.value].searchType,
            roomAndCustomerName: searchValue.value || undefined,
            // roomName: searchValue.value || undefined,
            // @ts-ignore
            projectId: currentProject?.id,
            // 添加筛选条件
            signDateStart: filterForm.value.signDateStart || undefined,
            signDateEnd: filterForm.value.signDateEnd || undefined,
            createTimeStart: filterForm.value.createTimeStart || undefined,
            createTimeEnd: filterForm.value.createTimeEnd || undefined,
            createByName: filterForm.value.createByName || undefined,
            signByName: filterForm.value.signByName || undefined,
            receivableDateStart: filterForm.value.receivableDateStart || undefined,
            receivableDateEnd: filterForm.value.receivableDateEnd || undefined,
            actualReceiveTimeStart: filterForm.value.actualReceiveTimeStart || undefined,
            actualReceiveTimeEnd: filterForm.value.actualReceiveTimeEnd || undefined,
            cancelTimeStart: filterForm.value.cancelTimeStart || undefined,
            cancelTimeEnd: filterForm.value.cancelTimeEnd || undefined,
            cancelByName: filterForm.value.cancelByName || undefined,
        }

        if (isAll.value) {
            // @ts-ignore
            delete params.projectId
            // params = {
            //     pageNum: pageNum.value,
            //     pageSize: pageSize.value,
            //     orderType: sortValue.value,
            //     searchType: statusTabs.value[activeTab.value].searchType,
            // }
        }

        const res = await getBookingList(params)
        if (res.code === 200) {
            if (pageNum.value === 1) {
                bookingList.value = res.rows
            } else {
                bookingList.value = [...bookingList.value, ...res.rows]
            }
            bookingList.value.forEach(item => {
                item.statusName = item.status === 0 ? '草稿' :
                    item.status === 1 ? '待收费' :
                        item.status === 2 ? '已生效' :
                            item.status === 3 ? '已转签' :
                                item.status === 4 ? '已作废' : '未知'
            })
            totalCount.value = res.total
            pageNum.value++
            finished.value = totalCount.value <= bookingList.value.length

        }
    } catch (error) {
        console.error('获取定单列表失败:', error)
        showToast('获取定单列表失败')
    } finally {
        loading.value = false
    }
}

// 重置列表
const resetList = () => {
    pageNum.value = 1
    bookingList.value = []
    finished.value = false
    fetchBookingList()
}

// 滚动加载更多
const onLoad = () => {
    fetchBookingList()
}

// 搜索
const handleSearch = () => {
    resetList()
}

// 切换状态标签
const handleTabChange = (index: number) => {
    activeTab.value = index
    //    // orderType：1-创建时间倒叙 2-应收日期倒叙 3-实收日期倒叙 4-签约日期倒叙 5-作废日期倒叙
    if (index === 0) {
        sortValue.value = '1'
        sortOptions.value = [
            { text: '最新创建', value: '1' },
            { text: '最新应收', value: '2' },
        ]
        resetStatus(index)

    } else if (index === 1) {
        sortValue.value = '3'
        //         最新实收：默认，实收日期倒叙
        // 最新创建：创建日期倒叙
        sortOptions.value = [
            { text: '最新实收', value: '3' },
            { text: '最新创建', value: '1' },
        ]
        resetStatus(index)
    } else if (index === 2) {
        /**
         * 最新签约：默认，签约日期倒叙
最新创建：创建日期倒叙*/
        sortValue.value = '4'
        sortOptions.value = [
            { text: '最新签约', value: '4' },
            { text: '最新创建', value: '1' },
        ]
        resetStatus(index)
    } else if (index === 3) {
        //最新作废：默认，作废日期倒叙
        // 最新创建：创建日期倒叙
        sortValue.value = '5'
        sortOptions.value = [
            { text: '最新作废', value: '5' },
            { text: '最新创建', value: '1' },
        ]
        resetStatus(index)
    }
    resetFilter()
    resetList()
}

const resetStatus = (index: number) => {
    filterForm.value.status = ''
    statusValue.value = []
    if (index === 0) {
        // { text: '草稿', value: 0 },
        // { text: '待收费', value: 1 },
        // { text: '已生效', value: 2 },
        // { text: '已转签', value: 3 },
        // { text: '已作废', value: 4 }
        statusColumns.value = [
            { text: '草稿', value: 0 },
            { text: '待收费', value: 1 },
        ]
    } else if (index === 1) {
        // statusColumns.value = [
        //     { text: '已生效', value: 2 }
        // ]
    } else if (index === 2) {
        // statusTabs.value[2].count = 0
    } else if (index === 3) { }

}

// 筛选变化
const handleSortChange = () => {
    // console.log(sortValue.value)
    // pageNum.value = 1
    // bookingList.value = []
    // finished.value = false
    // fetchBookingList()
    resetList()
}

// 退定
const handleRefund = (item: any) => {
    router.push({
        name: 'RefundBooking',
        params: { id: item.id },
        query: {
            customerName: item.customerName,
            roomName: item.roomName,
            bookingAmount: item.bookingAmount
        }
    })
}

// 显示收款码
const showQrCode = async (item: any) => {
    // try {
    // const baseUrl = 'http://172.30.1.254:8571'
    // const orderId = formData.id || `temp_${Date.now()}`
    // const amount = formData.depositAmount
    // const customerName = encodeURIComponent(formData.customerName || '')

    // 构建支付链接 - 这里可以根据实际的支付系统来构建
    const paymentLink = `${import.meta.env.VITE_APP_BASE_URL}/order-payment?id=${item.id}`

    qrCodeData.value = paymentLink
    showQRCodePopup.value = true
    // qrCodeLoading.value = true
    // currentBookingInfo.value = item

    // // 调用支付接口获取收款码
    // const paymentData: BookingPaymentDto = {
    //     bookingId: item.id,
    //     amount: item.unpaidAmount || item.bookingAmount
    // }

    // const res = await payBooking(paymentData)
    // if (res.data?.code === 200) {
    //     qrCodeData.value = res.data.data || res.data.paymentUrl || res.data.msg
    //     showQRCodePopup.value = true
    // } else {
    //     showToast(res.data?.msg || '获取收款码失败')
    // }
    // } catch (error) {
    //     console.error('获取支付链接失败:', error)
    //     showToast('获取支付链接失败')
    // } finally {
    //     qrCodeLoading.value = false
    // }
}

// 重新生成收款码
const regenerateQRCode = async () => {
    if (!currentBookingInfo.value.id) return
    await showQrCode(currentBookingInfo.value)
}

// 关闭收款码弹框
const closeQRCodePopup = () => {
    showQRCodePopup.value = false
    qrCodeData.value = ''
    currentBookingInfo.value = {}
}

// 显示筛选弹框
const showMoreFilter = () => {
    showFilterPopup.value = true
}

// 关闭筛选弹框
const closeFilterPopup = () => {
    showFilterPopup.value = false
}

// 应收款日期确认
const onReceivableDateConfirm = () => {
    console.log(receivableStartDate.value)
    console.log(receivableEndDate.value)
    filterForm.value.receivableDateStart = receivableStartDate.value[0] + '-' + receivableStartDate.value[1] + '-' + receivableStartDate.value[2]
    filterForm.value.receivableDateEnd = receivableEndDate.value[0] + '-' + receivableEndDate.value[1] + '-' + receivableEndDate.value[2]

    receivableDateRangeText.value = filterForm.value.receivableDateStart + ' 至 ' + filterForm.value.receivableDateEnd

    console.log(receivableDateRangeText.value)

    showReceivableDatePicker.value = false
}

// 应收款日期取消
const onReceivableDateCancel = () => {
    showReceivableDatePicker.value = false
}

// 签约日期确认
const onSignDateConfirm = () => {
    console.log(signStartDate.value)
    console.log(signEndDate.value)
    filterForm.value.signDateStart = signStartDate.value[0] + '-' + signStartDate.value[1] + '-' + signStartDate.value[2]
    filterForm.value.signDateEnd = signEndDate.value[0] + '-' + signEndDate.value[1] + '-' + signEndDate.value[2]

    signDateRangeText.value = filterForm.value.signDateStart + ' 至 ' + filterForm.value.signDateEnd

    console.log(signDateRangeText.value)

    showSignDatePicker.value = false
}

// 签约日期取消
const onSignDateCancel = () => {
    showSignDatePicker.value = false
}

// 创建日期确认
const onCreateTimeConfirm = () => {
    filterForm.value.createTimeStart = createStartDate.value[0] + '-' + createStartDate.value[1] + '-' + createStartDate.value[2]
    filterForm.value.createTimeEnd = createEndDate.value[0] + '-' + createEndDate.value[1] + '-' + createEndDate.value[2]

    createTimeRangeText.value = filterForm.value.createTimeStart + ' 至 ' + filterForm.value.createTimeEnd

    console.log(createTimeRangeText.value)

    showCreateTimePicker.value = false
}

// 创建日期取消
const onCreateTimeCancel = () => {
    showCreateTimePicker.value = false
}

// 实收日期确认
const onActualReceiveTimeConfirm = () => {
    filterForm.value.actualReceiveTimeStart = actualReceiveStartDate.value[0] + '-' + actualReceiveStartDate.value[1] + '-' + actualReceiveStartDate.value[2]
    filterForm.value.actualReceiveTimeEnd = actualReceiveEndDate.value[0] + '-' + actualReceiveEndDate.value[1] + '-' + actualReceiveEndDate.value[2]

    actualReceiveTimeRangeText.value = filterForm.value.actualReceiveTimeStart + ' 至 ' + filterForm.value.actualReceiveTimeEnd

    console.log(actualReceiveTimeRangeText.value)

    showActualReceiveTimePicker.value = false
}

// 实收日期取消
const onActualReceiveTimeCancel = () => {
    showActualReceiveTimePicker.value = false
}

// 作废日期确认
const onCancelTimeConfirm = () => {
    filterForm.value.cancelTimeStart = cancelStartDate.value[0] + '-' + cancelStartDate.value[1] + '-' + cancelStartDate.value[2]
    filterForm.value.cancelTimeEnd = cancelEndDate.value[0] + '-' + cancelEndDate.value[1] + '-' + cancelEndDate.value[2]

    cancelTimeRangeText.value = filterForm.value.cancelTimeStart + ' 至 ' + filterForm.value.cancelTimeEnd

    console.log(cancelTimeRangeText.value)

    showCancelTimePicker.value = false
}

// 作废日期取消
const onCancelTimeCancel = () => {
    showCancelTimePicker.value = false
}

// 应用筛选
const applyFilter = () => {
    showFilterPopup.value = false
    pageNum.value = 1
    bookingList.value = []
    finished.value = false
    totalCount.value = 0
    resetList()
}

// 重置筛选
const resetFilter = () => {
    filterForm.value = {
        status: '',
        signDateStart: '',
        signDateEnd: '',
        createTimeStart: '',
        createTimeEnd: '',
        createByName: '',
        signByName: '',
        receivableDateStart: '',
        receivableDateEnd: '',
        actualReceiveTimeStart: '',
        actualReceiveTimeEnd: '',
        cancelTimeStart: '',
        cancelTimeEnd: '',
        cancelByName: ''
    }
    // 重置日期选择器数据
    signStartDate.value = ['2024', '01', '01']
    signEndDate.value = ['2024', '12', '31']
    createStartDate.value = ['2024', '01', '01']
    createEndDate.value = ['2024', '12', '31']
    actualReceiveStartDate.value = ['2024', '01', '01']
    actualReceiveEndDate.value = ['2024', '12', '31']
    cancelStartDate.value = ['2024', '01', '01']
    cancelEndDate.value = ['2024', '12', '31']
    receivableStartDate.value = ['2024', '01', '01']
    receivableEndDate.value = ['2024', '12', '31']
    signDateRangeText.value = ''
    createTimeRangeText.value = ''
    actualReceiveTimeRangeText.value = ''
    cancelTimeRangeText.value = ''
    receivableDateRangeText.value = ''
    // statusValue.value = ''
    isAll.value = false
    resetList()
}

// 跳转详情
const goToDetail = (item: any) => {
    router.push({
        name: 'BookingDetail',
        params: { id: item.id }
    })
}

// 初始化
onMounted(() => {
    // console.log('onMounted', import.meta.env.VITE_APP_BASE_URL)
    fetchBookingList()
})

const getStatusBadgeClass = (status: string) => {
    const classMap: Record<string, string> = {
        '草稿': 'draft',
        '待收费': 'pending-payment',
        '已生效': 'active',
        '已转签': 'transferred',
        '已作废': 'cancelled'
    }
    return classMap[status] || 'default'
}

const goToCreate = () => {
    router.push({ name: 'BookingCreate' })
}

const handleDelete = (item: any) => {

    showConfirmDialog({
        title: '提示',
        message:
            '确定删除吗？',
    })
        .then(() => {
            // on confirm
            deleteBooking({ id: item.id }).then(res => {
                if (res.code === 200) {
                    showToast('删除成功')
                    resetList()
                }
            })
        })
        .catch(() => {
            // on cancel
        });

}

const handleInvalid = (item: any) => {
    showConfirmDialog({
        title: '提示',
        message: '确定作废吗？',
    })
        .then(() => {
            invalidBooking({ id: item.id, cancelRemark: '' }).then(res => {
                if (res.code === 200) {
                    showToast('作废成功')
                    resetList()
                }
            })
        })
        .catch(() => {
            // on cancel
        });
}

const getBookingSummaryMethod = (item: any) => {
    getBookingSummary({ id: item.id }).then(res => {
        console.log(res)
    })
}
</script>


<style lang="less" scoped>
.booking-list-page {
    background-color: #f5f5f5;
    min-height: 100vh;

    .add-img {
        width: 120px;
        height: 120px;
        position: fixed;
        bottom: 120px;
        right: 20px;
        z-index: 100;
    }
}

/* 导航栏样式 */
.custom-nav-bar {
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
}

/* 搜索栏样式 */
.search-section {
    height: 100px;
    background: #fff;
    box-sizing: border-box;
    overflow: hidden;
    padding: 20px 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-search {
    flex: 1;
    // background-color: #f8f9fa;
    // background: red;
    // box-sizing: border-box;
    padding: 0 !important;
    // border-radius: 30px !important;
}

.custom-search :deep(.van-search__content) {
    background-color: #f3f3f3;
    border-radius: 30px;
}

.cancel-btn {
    font-size: 32px;
    color: #666;
    cursor: pointer;
}

/* 状态标签栏 */
.status-tabs {
    height: 100px;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    background-color: #fff;
    padding: 0 20px;
    border-bottom: 1px solid #f0f0f0;
}

.status-tab {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 24px 0;
    cursor: pointer;
    position: relative;
}

.status-tab.active {
    color: #1890ff;
}

.status-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background-color: #1890ff;
    border-radius: 2px;
}

.tab-text {
    font-size: 30px;
    font-weight: 500;
}

.tab-count {
    font-size: 28px;
}

/* 筛选栏 */
.filter-section {
    height: 100px;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #F1F1F1;
    border-bottom: 1px solid #f0f0f0;
}

.filter-left {
    display: flex;
    align-items: center;
    /* gap: 8px; */
    border-radius: 20px;
}

.filter-dropdown {
    /* border-radius: 20px; */
}

:deep(.van-dropdown-menu__bar) {
    height: auto !important;
    border-radius: 40px;
    padding: 12px 30px;
    color: #000;
}

:deep(.van-ellipsis) {
    color: #000 !important;
}

.filter-right {

    display: flex;
    align-items: center;
    /* gap: 8px; */
}

.more-filter {
    background: #fff;
    border-radius: 40px;
    padding: 12px 30px;
    color: #000;
    display: flex;
    align-items: center;
    gap: 10px;

    .more-filter-img {
        width: 24px;
        height: 24px;
    }
}

.more-filter {
    font-size: 28px;
    color: #000;
    cursor: pointer;
}

/* 列表内容 */
.list-content {
    height: calc(100vh - 440px);
    padding: 20px;
    overflow-y: auto;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

.booking-item {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    cursor: pointer;
    transition: all 0.2s ease;
}

.booking-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.booking-item:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 蓝色渐变标题栏 */
.item-header {
    background: linear-gradient(135deg, #3583FF 0%, #5CBFFF 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 20px;
}

.item-title {
    font-size: 32px;
    font-weight: 500;
    color: #fff;
    flex: 1;
}

.status-badge {
    padding: 6px 16px;
    border-radius: 6px;
    font-size: 24px;
    font-weight: 500;
}

.status-badge.draft {
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.status-badge.pending-payment {
    background-color: #52c41a;
    color: #fff;
}

/* 白色信息区域 */
.item-content {
    display: flex;
    padding: 20px;
    background-color: #fff;
    position: relative;
}

.info-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: relative;

    .info-section-double {
        display: flex;
        gap: 12px;

        .info-item {
            width: 50%;
        }
    }
}

.info-item {
    display: flex;
    align-items: center;
    font-size: 28px;
    position: relative;
}

.info-label {
    color: #666;
    width: 140px;
    flex-shrink: 0;
}

.info-value {
    color: #333;
}

/* 右侧操作区域 */
.action-section {
    position: absolute;
    right: 16px;
    bottom: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    margin-left: 20px;
    min-height: 160px;
}

.qr-code {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 16px;
    min-height: 160px;
    box-sizing: border-box;

    .qr-code-img {
        width: 120px;
        height: 120px;
    }
}

.qr-label {
    font-size: 24px;
    color: #666;
}

.refund-btn {
    min-width: 140px;
    height: 56px;
    line-height: 56px;
    font-size: 28px;
    border-radius: 34px;
    // font-weight: 500;
}

/* 收款码弹框样式 */
.qr-code-popup {
    :deep(.van-popup) {
        border-radius: 20px;
        overflow: hidden;
    }

    .qr-code-content {
        padding: 40px;
        width: 600px;
        max-width: 90vw;
        background-color: #fff;
    }

    .qr-code-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;

        .qr-code-title {
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .close-icon {
            font-size: 40px;
            color: #999;
            cursor: pointer;
        }
    }

    .booking-info {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 12px;

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            font-size: 28px;

            &:last-child {
                margin-bottom: 0;
            }

            .label {
                color: #666;
            }

            .value {
                color: #333;
                font-weight: 500;

                &.amount {
                    color: #ff4444;
                    font-size: 32px;
                    font-weight: bold;
                }
            }
        }
    }

    .qr-code-section {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 30px;
        min-height: 200px;

        .loading-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
            color: #666;
            font-size: 28px;
        }

        .qr-code-wrapper {
            display: flex;
            justify-content: center;
            padding: 20px;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .error-section {
            color: #ff4444;
            font-size: 28px;
        }
    }

    .qr-code-actions {
        display: flex;
        justify-content: center;

        .action-btn {
            min-width: 200px;
            height: 80px;
            font-size: 30px;
        }
    }
}

/* 筛选弹框样式 */
.filter-popup {
    .filter-content {
        padding: 0;
        background-color: #fff;
    }

    .filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px 40px;
        border-bottom: 1px solid #f0f0f0;

        .filter-title {
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .close-icon {
            font-size: 40px;
            color: #999;
            cursor: pointer;
        }
    }

    .filter-form {
        padding: 0 20px;
        max-height: 50vh;
        overflow-y: auto;

        :deep(.van-cell-group) {
            margin-bottom: 20px;
        }

        :deep(.van-field__label) {
            font-size: 30px;
            color: #333;
            width: 140px;
        }

        :deep(.van-field__value) {
            font-size: 30px;
        }

        :deep(.van-field__control) {
            font-size: 30px;
        }
    }

    .filter-actions {
        display: flex;
        gap: 20px;
        padding: 30px 40px;
        border-top: 1px solid #f0f0f0;
        background-color: #fff;

        .reset-btn,
        .confirm-btn {
            flex: 1;
            height: 80px;
            font-size: 32px;
            border-radius: 8px;
        }

        .reset-btn {
            background-color: #f8f9fa;
            border-color: #e9ecef;
            color: #6c757d;
        }

        .confirm-btn {
            background-color: #1890ff;
            border-color: #1890ff;
        }
    }
}
</style>