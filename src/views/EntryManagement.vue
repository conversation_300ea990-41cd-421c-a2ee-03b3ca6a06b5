
<template>
    <div class="entry-management-page">
        <!-- 顶部导航 -->
        <van-nav-bar title="进场管理" left-arrow @click-left="$router.go(-1)" class="custom-nav-bar" />

        <!-- 搜索栏 -->
        <div class="search-section">
            <van-search v-model="searchValue" placeholder="请输入楼栋/房源/承租方" @search="handleSearch" @clear="resetList"
                class="custom-search" />
        </div>

        <!-- 状态标签栏 -->
        <div class="status-tabs">
            <div v-for="(tab, index) in statusTabs" :key="index" class="status-tab"
                :class="{ active: activeTab === index }" @click="handleTabChange(index)">
                <span class="tab-text">{{ tab.label }}</span>
            </div>
            <div class="time-filter">
                <van-dropdown-menu class="time-dropdown">
                    <van-dropdown-item v-model="timeFilterValue" :options="timeFilterOptions"
                        @change="handleTimeFilterChange" />
                </van-dropdown-menu>
            </div>
        </div>

        <!-- 筛选栏 -->
        <!-- <div class="filter-section">
            <div class="filter-left">
                <van-dropdown-menu class="filter-dropdown">
                    <van-dropdown-item 
                        v-model="sortValue" 
                        :options="sortOptions" 
                        @change="handleSortChange" 
                    />
                </van-dropdown-menu>
            </div>
            <div class="filter-right">
                <span class="more-filter" @click="showMoreFilter">
                    <span class="more-filter-text">更多筛选</span>
                    <img src="@/assets/images/filter.png" class="more-filter-img" />
                </span>
            </div>
        </div> -->

        <!-- 列表内容 -->
        <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
            class="list-content"
        >
            <div v-for="item in entryList" :key="item.id" class="entry-item" @click="goToDetail(item)">
                <!-- 标题栏 -->
                <moduleTitle :title="item.tenantName" :status="item.statusName || ''" :statusCode="item.status" />

                <!-- 信息区域 -->
                <div class="item-content">
                    <div class="info-section">
                        <div class="info-item">
                            <span class="info-label">合同编号：</span>
                            <span class="info-value">{{ item.contractNo }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">租期：</span>
                            <span class="info-value">{{ item.rentStartDate }} 至 {{ item.rentEndDate }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">未进场房源数：</span>
                            <span class="info-value">{{ item.unenterNum }}</span>
                        </div>
                        <div class="info-item info-handle">
                            <van-button type="primary" size="small" round @click.stop="handleEntryProcess(item)"
                                class="action-btn" v-if="item.status === 0">
                                办理进场
                            </van-button>

                            <van-button type="primary" size="small" round @click.stop="handleAdjust(item)"
                                class="action-btn" v-if="item.status === 1">
                                调整
                            </van-button>
                            <van-button type="warning" size="small" round @click.stop="handleNotifyCustomer(item)"
                                class="action-btn notify-btn" v-if="item.status === 1">
                                通知客户
                            </van-button>
                            <van-button type="default" size="small" round @click.stop="handleViewDetail(item)"
                                class="action-btn detail-btn" v-if="item.status === 1">
                                查看
                            </van-button>
                        </div>
                    </div>

                    <!-- 右侧操作区域 -->
                    <!-- <div class="action-section">
                        <van-button 
                            type="primary" 
                            size="small" 
                            round 
                            @click.stop="handleEntryProcess(item)"
                            class="action-btn"
                            v-if="item.status === 0"
                        >
                            办理进场
                        </van-button>
                    </div> -->
                </div>
            </div>
            <div class="empty-content" v-if="entryList.length === 0 && !loading">
                <van-empty description="暂无数据" />
            </div>
        </van-list>

        <!-- 更多筛选弹框 -->
        <van-popup v-model:show="showFilterPopup" position="bottom" :style="{ height: '70%' }" round
            class="filter-popup">
            <div class="filter-content">
                <div class="filter-header">
                    <h3 class="filter-title">更多筛选</h3>
                    <van-icon name="cross" @click="closeFilterPopup" class="close-icon" />
                </div>

                <div class="filter-form">
                    <!-- 进场日期 -->
                    <van-cell-group>
                        <van-field readonly clickable label="进场日期" v-model="entryDateRangeText" placeholder="请选择进场日期区间"
                            @click="showEntryDatePicker = true" />
                    </van-cell-group>

                    <!-- 创建日期 -->
                    <van-cell-group>
                        <van-field readonly clickable label="创建日期" v-model="createTimeRangeText" placeholder="请选择创建日期区间"
                            @click="showCreateTimePicker = true" />
                    </van-cell-group>

                    <!-- 合同编号 -->
                    <van-cell-group>
                        <van-field v-model="filterForm.contractNo" label="合同编号" placeholder="请输入合同编号" clearable />
                    </van-cell-group>

                    <!-- 创建人 -->
                    <van-cell-group>
                        <van-field v-model="filterForm.createByName" label="创建人" placeholder="请输入创建人姓名" clearable />
                    </van-cell-group>
                </div>

                <!-- 进场日期选择器 -->
                <van-popup v-model:show="showEntryDatePicker" position="bottom">
                    <van-picker-group title="进场日期" :tabs="['开始日期', '结束日期']" @confirm="onEntryDateConfirm"
                        @cancel="onEntryDateCancel">
                        <van-date-picker v-model="entryStartDate" :min-date="minDate" :max="maxDate" />
                        <van-date-picker v-model="entryEndDate" :min-date="entryEndMinDate" :max="maxDate" />
                    </van-picker-group>
                </van-popup>

                <!-- 创建日期选择器 -->
                <van-popup v-model:show="showCreateTimePicker" position="bottom">
                    <van-picker-group title="创建日期" :tabs="['开始日期', '结束日期']" @confirm="onCreateTimeConfirm"
                        @cancel="onCreateTimeCancel">
                        <van-date-picker v-model="createStartDate" :min-date="minDate" :max-date="maxDate" />
                        <van-date-picker v-model="createEndDate" :min-date="createEndMinDate" :max-date="maxDate" />
                    </van-picker-group>
                </van-popup>

                <div class="filter-actions">
                    <van-button class="reset-btn" @click="resetFilter">重置</van-button>
                    <van-button type="primary" class="confirm-btn" @click="applyFilter">确定</van-button>
                </div>
            </div>
        </van-popup>

        <!-- 房源选择弹框 -->
        <van-popup v-model:show="showRoomSelectionPopup" position="bottom" :style="{ height: '85%' }" round
            class="room-selection-popup">
            <div class="room-selection-content">
                <!-- 弹框头部 -->
                <div class="room-selection-header">
                    <h3 class="room-selection-title">选择进场房源</h3>
                    <van-icon name="cross" @click="closeRoomSelectionPopup" class="close-icon" />
                </div>

                <!-- 搜索栏 -->
                <div class="room-search-section">
                    <van-search v-model="roomSearchValue" placeholder="请输入楼栋/房源名称" @search="searchRooms"
                        @input="handleRoomSearchInput" @clear="handleRoomSearchInput" class="room-search" />
                </div>

                <!-- 房源列表 -->
                <div class="room-list-section">
                    <van-loading v-if="roomSelectionLoading" type="spinner" />
                    <div v-else class="room-list">
                        <!-- 全选选项 -->
                        <div class="room-item all-select-item" @click="toggleSelectAll">
                            <van-radio :checked="isAllSelected" class="room-radio" />
                            <span class="room-name">全选</span>
                        </div>

                        <!-- 房源列表 -->
                        <div v-for="room in availableRooms" :key="room.id" class="room-item"
                            @click="toggleRoomSelection(room.id)">
                            <van-radio :checked="selectedRooms.includes(room.id)" class="room-radio" />
                            <span class="room-name">{{ room.roomName }}</span>
                        </div>
                    </div>
                </div>

                <!-- 底部确认按钮 -->
                <div class="room-selection-footer">
                    <van-button type="primary" class="confirm-selection-btn" @click="confirmEntryProcess"
                        :disabled="selectedRooms.length === 0">
                        确定 {{ selectedRooms.length > 0 ? `(${selectedRooms.length})` : '' }}
                    </van-button>
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import moduleTitle from '@/components/ModuleTitle.vue'
import { getEntryList, initEntry, getUnenteredRooms, getEnterDetail, notifyCustomer, type EnterQueryDTO, type EnterVo } from '../api/entry'
import { showToast, showConfirmDialog } from 'vant'

const router = useRouter()

// 搜索相关
const searchValue = ref('')
const pageNum = ref(1)
const pageSize = ref(10)

// 状态标签
const activeTab = ref(0)
const statusTabs = ref([
    { label: '待办理', count: 0, searchType: 0 },
    { label: '已办理', count: 0, searchType: 1 }
])

// 时间筛选
const timeFilterValue = ref('全部')
const timeFilterOptions = [
    { text: '近3天', value: '近3天' },
    { text: '近7天', value: '近7天' },
    { text: '近30天', value: '近30天' },
    { text: '全部', value: '全部' }
]

// 排序筛选
const sortValue = ref('1')
const sortOptions = [
    { text: '最新创建', value: '1' },
    { text: '进场日期', value: '2' },
    { text: '合同编号', value: '3' }
]

// 列表数据
const entryList = ref<EnterVo[]>([])
const loading = ref(false)
const finished = ref(false)

// 更多筛选弹框相关
const showFilterPopup = ref(false)
const filterForm = ref({
    entryDateStart: '',    // 进场日期开始
    entryDateEnd: '',      // 进场日期结束
    createTimeStart: '',   // 创建日期开始
    createTimeEnd: '',     // 创建日期结束
    createByName: '',      // 创建人
    contractNo: ''         // 合同编号
})

// 日期区间和最小最大值限制
const minDate = new Date(2020, 0, 1)
const maxDate = new Date(2030, 11, 31)

// 日期选择器相关
const showEntryDatePicker = ref(false)
const showCreateTimePicker = ref(false)

// 进场日期选择
const entryStartDate = ref(['2024', '01', '01'])
const entryEndDate = ref(['2024', '12', '31'])
const entryEndMinDate = computed(() =>
    new Date(
        Number(entryStartDate.value[0]),
        Number(entryStartDate.value[1]) - 1,
        Number(entryStartDate.value[2])
    )
)

// 创建日期选择
const createStartDate = ref(['2024', '01', '01'])
const createEndDate = ref(['2024', '12', '31'])
const createEndMinDate = computed(() =>
    new Date(
        Number(createStartDate.value[0]),
        Number(createStartDate.value[1]) - 1,
        Number(createStartDate.value[2])
    )
)

// 计算属性：日期区间显示文本
const entryDateRangeText = ref('')
const createTimeRangeText = ref('')

// 获取列表数据
const fetchEntryList = async () => {
    let currentProject = localStorage.getItem('currentProject')
    if (currentProject) {
        currentProject = JSON.parse(currentProject)
    } else {

    }
    try {
        loading.value = true
        const params: EnterQueryDTO = {
            pageNum: pageNum.value,
            pageSize: pageSize.value,
            type: statusTabs.value[activeTab.value].searchType.toString(), // 0-待办理,1-已办理
            tenantName: searchValue.value || undefined, // 承租方名称，模糊匹配
            roomAndCustomerName: searchValue.value || undefined, // 楼栋/房源/承租方
            // projectId: '108', // 默认项目ID
            // @ts-ignore
            projectId: currentProject?.id,
            // 添加筛选条件
            rentStartDateBegin: filterForm.value.entryDateStart || undefined,
            rentStartDateEnd: filterForm.value.entryDateEnd || undefined,
            createByName: filterForm.value.createByName || undefined,
        }

        // 添加时间筛选逻辑
        if (timeFilterValue.value !== '全部') {
            const now = new Date()
            const daysMap = { '近3天': 3, '近7天': 7, '近30天': 30 }
            const days = daysMap[timeFilterValue.value as keyof typeof daysMap]
            if (days) {
                params.nearDays = days
            }
        }

        const res = await getEntryList(params)
        console.log(res)
        if (res.code === 200 && res.rows) {
            const newItems = Array.isArray(res.rows) ? res.rows : []
            
            if (pageNum.value === 1) {
                entryList.value = newItems
            } else {
                entryList.value = [...entryList.value, ...newItems]
            }
            
            entryList.value.forEach((item: EnterVo) => {
                // 根据未进场房源数判断状态
                item.status = item.unenterNum > 0 ? 0 : 1 // 0-待办理 1-已办理
                item.statusName = item.status === 0 ? '待办理' : '已办理'
            })
            pageNum.value++
            finished.value = res.total <= entryList.value.length
        }
    } catch (error) {
        console.error('获取进场管理列表失败:', error)
        showToast('获取进场管理列表失败')
    } finally {
        loading.value = false
    }
}

// 重置列表
const resetList = () => {
    pageNum.value = 1
    entryList.value = []
    finished.value = false
    fetchEntryList()
}

// 滚动加载更多
const onLoad = () => {
    fetchEntryList()
}

// 搜索
const handleSearch = () => {
    resetList()
}

// 切换状态标签
const handleTabChange = (index: number) => {
    activeTab.value = index
    resetList()
}

// 排序变化
const handleSortChange = () => {
    resetList()
}

// 时间筛选变化
const handleTimeFilterChange = () => {
    resetList()
}

// 房源选择相关
const showRoomSelectionPopup = ref(false)
const currentEntryItem = ref<EnterVo | null>(null)
const roomSearchValue = ref('')
const availableRooms = ref<any[]>([])
const selectedRooms = ref<string[]>([])
const roomSelectionLoading = ref(false)

// 办理进场
const handleEntryProcess = async (item: EnterVo) => {
    currentEntryItem.value = item
    showRoomSelectionPopup.value = true
    await fetchAvailableRooms(item.contractId)
}

// 调整进场信息
const handleAdjust = async (item: EnterVo) => {
    try {
        // 获取进场单详情
        const res = await getEnterDetail(item.id)

        if (res.code === 200 && res.data) {
            showToast('加载详情成功')

            // 从返回数据中获取contract信息
            const contract = res.data.contract || {}
            const enter = res.data.enter || res.data

            // 跳转到进场办理页面，传递详情数据和编辑模式
            router.push({
                name: 'EntryProcess',
                query: {
                    entryId: item.id,
                    contractId: item.contractId,
                    contractUnionId: contract.unionId || enter.contractUnionId || '', // 从contract获取unionId
                    projectId: enter.projectId || item.projectId || '', // 从enter或item获取projectId
                    contractNo: item.contractNo,
                    tenantName: item.tenantName,
                    mode: 'edit' // 标识为编辑模式
                },
                state: {
                    entryData: res.data // 通过路由状态传递详情数据
                }
            })
        }
    } catch (error) {
        console.error('获取进场详情失败:', error)
        showToast('获取进场详情失败')
    }
}

// 通知客户
const handleNotifyCustomer = async (item: EnterVo) => {
    try {
        await showConfirmDialog({
            title: '确认通知客户',
            message: `确定要给承租方"${item.tenantName}"发送进场通知单吗？`,
        })

        const res = await notifyCustomer(item.id)

        if (res.code === 200) {
            showToast('通知发送成功')
        } else {
            showToast(res.msg || '通知发送失败')
        }
    } catch (error) {
        console.error('通知客户失败:', error)
        showToast('通知客户失败')
    }
}

// 获取可选房源列表
const fetchAvailableRooms = async (contractId: string) => {
    try {
        roomSelectionLoading.value = true

        // 调用API获取该合同的待进场房源列表
        const params: EnterQueryDTO = {
            pageNum: 1,
            pageSize: 100,
            contractId: contractId
        }
        const res = await getUnenteredRooms(params)
        if (res.data) {
            const rooms = res.data.map((room: any) => ({
                id: room.id,
                roomName: room.roomName,
                buildingName: room.buildingName,
                isEntered: room.isEntered
            })).filter((room: any) => !room.isEntered) // 只显示未进场的房源

            originalRooms.value = [...rooms]
            availableRooms.value = [...rooms]
        } else {
            // 如果API调用失败，使用模拟数据
            const mockRooms = [
                { id: '1', roomName: '北区-56幢-101', isSelected: false },
                { id: '2', roomName: '北区-56幢-102', isSelected: false },
                { id: '3', roomName: '北区-56幢-103', isSelected: false },
                { id: '4', roomName: '北区-57幢-101', isSelected: false },
                { id: '5', roomName: '北区-57幢-102', isSelected: false },
            ]
            originalRooms.value = [...mockRooms]
            availableRooms.value = [...mockRooms]
        }
    } catch (error) {
        console.error('获取房源列表失败:', error)
        showToast('获取房源列表失败')

        // 发生错误时使用模拟数据
        const errorMockRooms = [
            { id: '1', roomName: '北区-56幢-101', isSelected: false },
            { id: '2', roomName: '北区-56幢-102', isSelected: false },
            { id: '3', roomName: '北区-56幢-103', isSelected: false },
        ]
        originalRooms.value = [...errorMockRooms]
        availableRooms.value = [...errorMockRooms]
    } finally {
        roomSelectionLoading.value = false
    }
}

// 原始房源列表
const originalRooms = ref<any[]>([])

// 搜索房源
const searchRooms = () => {
    const searchText = roomSearchValue.value.trim().toLowerCase()
    if (!searchText) {
        availableRooms.value = [...originalRooms.value]
        return
    }

    availableRooms.value = originalRooms.value.filter(room =>
        room.roomName.toLowerCase().includes(searchText) ||
        (room.buildingName && room.buildingName.toLowerCase().includes(searchText))
    )
}

// 监听搜索框变化
const handleRoomSearchInput = () => {
    searchRooms()
}

// 切换房源选择状态
const toggleRoomSelection = (roomId: string) => {
    const index = selectedRooms.value.indexOf(roomId)
    if (index > -1) {
        selectedRooms.value.splice(index, 1)
    } else {
        selectedRooms.value.push(roomId)
    }
}

// 全选/取消全选
const toggleSelectAll = () => {
    if (selectedRooms.value.length === availableRooms.value.length) {
        // 当前全选状态，取消全选
        selectedRooms.value = []
    } else {
        // 未全选，执行全选
        selectedRooms.value = availableRooms.value.map(room => room.id)
    }
}

// 检查是否全选
const isAllSelected = computed(() => {
    return availableRooms.value.length > 0 && selectedRooms.value.length === availableRooms.value.length
})

// 确认进场办理
const confirmEntryProcess = async () => {
    if (selectedRooms.value.length === 0) {
        showToast('请选择要办理进场的房源')
        return
    }

    try {
        await showConfirmDialog({
            title: '确认办理进场',
            message: `确定要办理 ${selectedRooms.value.length} 个房源的进场吗？`,
        })

        // 调用进场单初始化API
        const res = await initEntry({
            contractId: currentEntryItem.value!.contractId,
            roomIds: selectedRooms.value
        })

        console.log('initEntry返回数据:', res)

        if (res.data) {
            showToast('初始化成功')
            showRoomSelectionPopup.value = false

            // 跳转到进场办理页面，通过路由状态传递初始化后的详情数据
            router.push({
                name: 'EntryProcess',
                query: {
                    // entryId: res.data.id || res.data,
                    contractId: currentEntryItem.value!.contractId,
                    roomIds: selectedRooms.value.join(','),
                    contractNo: currentEntryItem.value!.contractNo,
                    tenantName: currentEntryItem.value!.tenantName
                },
                state: {
                    entryData: res.data // 通过路由状态传递详情数据
                }
            })
        }
    } catch (error) {
        console.error('办理进场失败:', error)
        showToast('办理进场失败')
    }
}

// 关闭房源选择弹框
const closeRoomSelectionPopup = () => {
    showRoomSelectionPopup.value = false
    currentEntryItem.value = null
    selectedRooms.value = []
    roomSearchValue.value = ''
    availableRooms.value = []
    originalRooms.value = []
}

// 显示筛选弹框
const showMoreFilter = () => {
    showFilterPopup.value = true
}

// 关闭筛选弹框
const closeFilterPopup = () => {
    showFilterPopup.value = false
}

// 进场日期确认
const onEntryDateConfirm = () => {
    filterForm.value.entryDateStart = entryStartDate.value.join('-')
    filterForm.value.entryDateEnd = entryEndDate.value.join('-')
    entryDateRangeText.value = `${filterForm.value.entryDateStart} 至 ${filterForm.value.entryDateEnd}`
    showEntryDatePicker.value = false
}

// 进场日期取消
const onEntryDateCancel = () => {
    showEntryDatePicker.value = false
}

// 创建日期确认
const onCreateTimeConfirm = () => {
    filterForm.value.createTimeStart = createStartDate.value.join('-')
    filterForm.value.createTimeEnd = createEndDate.value.join('-')
    createTimeRangeText.value = `${filterForm.value.createTimeStart} 至 ${filterForm.value.createTimeEnd}`
    showCreateTimePicker.value = false
}

// 创建日期取消
const onCreateTimeCancel = () => {
    showCreateTimePicker.value = false
}

// 应用筛选
const applyFilter = () => {
    showFilterPopup.value = false
    resetList()
}

// 重置筛选
const resetFilter = () => {
    filterForm.value = {
        entryDateStart: '',
        entryDateEnd: '',
        createTimeStart: '',
        createTimeEnd: '',
        createByName: '',
        contractNo: ''
    }
    // 重置日期选择器数据
    entryStartDate.value = ['2024', '01', '01']
    entryEndDate.value = ['2024', '12', '31']
    createStartDate.value = ['2024', '01', '01']
    createEndDate.value = ['2024', '12', '31']
    entryDateRangeText.value = ''
    createTimeRangeText.value = ''
    resetList()
}

// 跳转详情
const goToDetail = (item: EnterVo) => {
    router.push({
        name: 'EntranceNotice',
        query: { contractId: item.contractId, entryId: item.id }
    })
}

// 查看详情
const handleViewDetail = (item: EnterVo) => {
    router.push({
        name: 'EntryDetail',
        query: {
            entryId: item.id,
            contractId: item.contractId,
            contractNo: item.contractNo,
            tenantName: item.tenantName
        }
    })
}

// 获取状态徽章类型
const getStatusBadgeClass = (status: number) => {
    return status === 0 ? 'pending' : 'completed'
}

// 初始化
onMounted(() => {
    fetchEntryList()
})
</script>


<style lang="less" scoped>
.entry-management-page {
    background-color: #f5f5f5;
    min-height: 100vh;
}

/* 导航栏样式 */
.custom-nav-bar {
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
}

/* 搜索栏样式 */
.search-section {
    height: 100px;
    background: #fff;
    box-sizing: border-box;
    overflow: hidden;
    padding: 20px 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-search {
    flex: 1;
    padding: 0 !important;
}

.custom-search :deep(.van-search__content) {
    background-color: #f3f3f3;
    border-radius: 30px;
}

/* 状态标签栏 */
.status-tabs {
    height: 100px;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    background-color: #fff;
    padding: 0 20px;
    border-bottom: 1px solid #f0f0f0;
    align-items: center;
}

.status-tab {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 24px 0;
    cursor: pointer;
    position: relative;
}

.status-tab.active {
    color: #1890ff;
}

.status-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background-color: #1890ff;
    border-radius: 2px;
}

.tab-text {
    font-size: 30px;
    font-weight: 500;
}

.time-filter {
    flex-shrink: 0;
    margin-left: 20px;
}

.time-dropdown {
    background: #f8f9fa;
    border-radius: 20px;
    padding: 8px 16px;
}

.time-dropdown :deep(.van-dropdown-menu__bar) {
    height: auto !important;
    background: transparent;
    border: none;
    padding: 0;
}

.time-dropdown :deep(.van-ellipsis) {
    color: #666 !important;
    font-size: 28px;
}

/* 筛选栏 */
.filter-section {
    height: 100px;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #F1F1F1;
    border-bottom: 1px solid #f0f0f0;
}

.filter-left {
    display: flex;
    align-items: center;
    border-radius: 20px;
}

.filter-dropdown :deep(.van-dropdown-menu__bar) {
    height: auto !important;
    border-radius: 40px;
    padding: 12px 30px;
    color: #000;
}

.filter-dropdown :deep(.van-ellipsis) {
    color: #000 !important;
}

.filter-right {
    display: flex;
    align-items: center;
}

.more-filter {
    background: #fff;
    border-radius: 40px;
    padding: 12px 30px;
    color: #000;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 28px;
    cursor: pointer;

    .more-filter-img {
        width: 24px;
        height: 24px;
    }
}

/* 列表内容 */
.list-content {
    height: calc(100vh - 300px);
    padding: 20px;
    overflow-y: auto;
}

.empty-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.entry-item {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    cursor: pointer;
    transition: all 0.2s ease;
}

.entry-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.entry-item:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 信息区域 */
.item-content {
    display: flex;
    padding: 20px;
    background-color: #fff;
}

.info-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .info-handle {
        // margin-top: 20px;
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        flex-wrap: wrap;
    }
}

.info-item {
    display: flex;
    align-items: center;
    font-size: 28px;
}

.info-label {
    color: #888;
    // width: 200px;
    flex-shrink: 0;
}

.info-value {
    color: #333;
}

/* 右侧操作区域 */
.action-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
}

.action-btn {
    min-width: 140px;
    height: 56px;
    line-height: 56px;
    font-size: 28px;
    border-radius: 34px;
}

.notify-btn {
    background: #ff9500;
    border-color: #ff9500;
    color: #fff;
}

.detail-btn {
    background: #f8f9fa;
    border-color: #f8f9fa;
    color: #333;
}

/* 筛选弹框样式 */
.filter-popup {
    .filter-content {
        padding: 0;
        background-color: #fff;
    }

    .filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px 40px;
        border-bottom: 1px solid #f0f0f0;

        .filter-title {
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .close-icon {
            font-size: 40px;
            color: #999;
            cursor: pointer;
        }
    }

    .filter-form {
        padding: 0 20px;
        max-height: 50vh;
        overflow-y: auto;

        :deep(.van-cell-group) {
            margin-bottom: 20px;
        }

        :deep(.van-field__label) {
            font-size: 30px;
            color: #333;
            width: 140px;
        }

        :deep(.van-field__value) {
            font-size: 30px;
        }

        :deep(.van-field__control) {
            font-size: 30px;
        }
    }

    .filter-actions {
        display: flex;
        gap: 20px;
        padding: 30px 40px;
        border-top: 1px solid #f0f0f0;
        background-color: #fff;

        .reset-btn,
        .confirm-btn {
            flex: 1;
            height: 80px;
            font-size: 32px;
            border-radius: 8px;
        }

        .reset-btn {
            background-color: #f8f9fa;
            border-color: #e9ecef;
            color: #6c757d;
        }

        .confirm-btn {
            background-color: #1890ff;
            border-color: #1890ff;
        }
    }
}

/* 房源选择弹框样式 */
.room-selection-popup {
    .room-selection-content {
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: #fff;
    }

    .room-selection-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px 40px;
        border-bottom: 1px solid #f0f0f0;
        flex-shrink: 0;

        .room-selection-title {
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .close-icon {
            font-size: 40px;
            color: #999;
            cursor: pointer;
        }
    }

    .room-search-section {
        padding: 20px 30px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #f0f0f0;
        flex-shrink: 0;

        .room-search {
            padding: 0 !important;
        }

        .room-search :deep(.van-search__content) {
            background-color: #fff;
            border-radius: 30px;
            border: 1px solid #e0e0e0;
        }

        .room-search :deep(.van-search__field) {
            padding: 12px 20px;
            font-size: 28px;
        }
    }

    .room-list-section {
        flex: 1;
        overflow-y: auto;
        padding: 0 30px;

        .van-loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .room-list {
            padding: 20px 0;
        }

        .room-item {
            display: flex;
            align-items: center;
            padding: 24px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:hover {
                background-color: #f8f9fa;
            }

            &:last-child {
                border-bottom: none;
            }

            &.all-select-item {
                background-color: #f8f9fa;
                border-radius: 12px;
                padding: 24px 20px;
                margin-bottom: 20px;
                border-bottom: none;

                .room-name {
                    font-weight: 500;
                    color: #333;
                }
            }

            .room-radio {
                margin-right: 20px;
                flex-shrink: 0;
            }

            .room-radio :deep(.van-radio__icon) {
                font-size: 40px;
            }

            .room-name {
                font-size: 30px;
                color: #333;
                flex: 1;
            }
        }
    }

    .room-selection-footer {
        padding: 30px 40px;
        border-top: 1px solid #f0f0f0;
        background-color: #fff;
        flex-shrink: 0;

        .confirm-selection-btn {
            width: 100%;
            height: 80px;
            font-size: 32px;
            border-radius: 12px;

            &:disabled {
                background-color: #f5f5f5;
                border-color: #f5f5f5;
                color: #c8c9cc;
            }
        }
    }
}
</style>