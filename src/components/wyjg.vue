<!-- 物业交割 -->
<template>
    <div class="wyjg-container" :class="{'wyjg-container-bottom': exitMode === 'property-only' && exitSep === 1}">
        <!-- 进场房源 -->
        <div class="rooms-section">
            <div class="section-title">
                <img src="../assets/images/entrance-notice-icon.png" alt="房源" class="title-icon" />
                <div class="section-title-text">
                    出场房源 · <span class="room-count">{{ roomList.length }}间</span>
                </div>
                <!-- <div class="section-title-right">
                    <div class="section-title-right-item">
                        <div class="section-title-right-item-tag"></div>
                        <div class="section-title-right-item-text">
                            未确认
                        </div>
                    </div>
                    <div class="section-title-right-item">
                        <div class="section-title-right-item-tag"></div>
                        <div class="section-title-right-item-text">
                            已确认
                        </div>
                    </div>
                </div> -->
            </div>
            <div class="room-list-content">
                <!-- 全选 -->
                <div class="all-select">
                    <div class="all-select-left">
                        <van-checkbox v-model="isAllSelected" @change="onAllSelectChange" />全选
                    </div>
                    <div class="all-select-btn">
                        <van-button type="default" size="mini" @click="handleBatchExitDate">批量设置出场日期</van-button>
                        <van-button type="primary" size="mini" @click="handleBatchBusinessConfirm">商服批量确认</van-button>
                        <van-button type="default" size="mini"
                            @click="handleCopyPropertyConfirmUrl">复制物业确认单地址</van-button>
                    </div>
                </div>
                <!-- 房源列表 -->
                <div class="room-list">
                    <div v-for="(room, roomIndex) in roomList" :key="room.roomId" class="room-card">
                        <!-- 房间头部 -->
                        <div class="room-header" @click="toggleRoomExpand(room.roomId)">
                            <div class="room-header-left">
                                <van-checkbox v-model="room.isSelected" @click.stop="onRoomSelectChange(roomIndex)" />
                            </div>
                            <div class="room-info">
                                <div class="room-name-row">
                                    <img class="house-icon" src="../assets/images/house-icon.svg" alt="房源" />
                                    <span class="room-name">{{ room.roomName }}</span>
                                    <span class="room-name-tag"
                                        :class="room.isBusinessConfirmed ? 'room-name-tag-on' : ''">商</span>
                                    <span class="room-name-tag"
                                        :class="room.isEngineeringConfirmed && room.isFinanceConfirmed ? 'room-name-tag-on' : ''">物</span>
                                </div>
                                <div class="room-date">出场日期：{{ formatDate(room.exitDate || '') }}</div>
                            </div>
                            <van-icon :name="expandedRooms.includes(room.roomId) ? 'arrow-up' : 'arrow-down'"
                                class="expand-icon" />
                        </div>

                        <!-- 房间详情 -->
                        <div v-if="expandedRooms.includes(room.roomId)" class="room-content">
                            <!-- 出场日期和租控管理 -->
                            <div class="form-row">
                                <van-field label="出场日期" readonly :is-link="!isViewMode" input-align="right"
                                    label-width="100px" :model-value="formatDate(room.exitDate || '')"
                                    placeholder="请选择出场日期" @click="!isViewMode && showDatePicker(roomIndex)" />
                            </div>

                            <div class="form-row">
                                <van-field label="租控管理" readonly :is-link="!isViewMode" input-align="right"
                                    label-width="100px" :model-value="getRentControlText(room.rentControl)"
                                    placeholder="请选择租控管理" @click="!isViewMode && showRentControlPicker(roomIndex)" />
                            </div>

                            <!-- 房间配套情况 -->
                            <div class="assets-section">
                                <div class="section-title-blue">
                                    <div class="section-title-blue-text">
                                        <span class="tag"></span>
                                        房间配套情况
                                    </div>
                                    <van-button v-if="!isViewMode" type="primary" size="mini"
                                        @click="showAssetSelector(roomIndex)" class="add-btn">
                                        + 添加配套
                                    </van-button>
                                </div>

                                <div v-if="!room.assetList || room.assetList.length === 0" class="empty-assets">
                                    暂无配套设施
                                </div>

                                <div v-for="(asset, assetIndex) in room.assetList" :key="assetIndex" class="asset-item">
                                    <div class="asset-info">
                                        <div class="asset-header">
                                            <div class="asset-label-container">
                                                <van-icon v-if="asset.isAdd && !isViewMode" name="delete-o"
                                                    class="delete-icon" @click="removeAsset(roomIndex, assetIndex)" />
                                                <span class="asset-name">{{ asset.name }}{{ asset.specification ?
                                                    `(${asset.specification})` : '' }}</span>
                                            </div>
                                            <div class="asset-status-container">
                                                <span class="status-label">现状：</span>
                                                <van-dropdown-menu>
                                                    <van-dropdown-item v-model="asset.status"
                                                        :options="assetStatusOptions" :disabled="isViewMode"
                                                        @change="onAssetStatusChange(roomIndex, assetIndex)" />
                                                </van-dropdown-menu>
                                            </div>
                                        </div>
                                        <div class="asset-details" v-if="asset.status === 2 || asset.status === 3">
                                            <van-field v-model="asset.penalty" label="赔偿金" type="number"
                                                input-align="right" placeholder="输入赔偿金额" :readonly="isViewMode"
                                                @update:model-value="onAssetPenaltyChange(roomIndex, assetIndex)">
                                                <template #button>
                                                    <span class="unit">元</span>
                                                </template>
                                            </van-field>
                                            <van-field v-model="asset.remark" label="说明" placeholder="请输入说明"
                                                :readonly="isViewMode" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 房屋其他情况 -->
                            <div class="other-section">
                                <div class="section-title-blue">
                                    <div class="section-title-blue-text">
                                        <span class="tag"></span>
                                        房屋其他情况
                                    </div>
                                </div>

                                <!-- 门窗墙体 -->
                                <div class="form-item">
                                    <van-field label="门、窗、墙体及其他" readonly :is-link="!isViewMode" input-align="right"
                                        label-width="180px"
                                        :model-value="getDoorWindowStatusText(room.doorWindowStatus)"
                                        placeholder="请选择状态"
                                        @click="!isViewMode && showDoorWindowStatusPicker(roomIndex)" />
                                </div>
                                <div v-if="room.doorWindowStatus === 2" class="penalty-row">
                                    <van-field v-model="room.doorWindowPenalty" label="赔偿金额" type="number"
                                        input-align="right" placeholder="输入赔偿金额" :readonly="isViewMode"
                                        @update:model-value="onDoorWindowPenaltyChange(roomIndex)">
                                        <template #button>
                                            <span class="unit">元从押金中扣除</span>
                                        </template>
                                    </van-field>
                                </div>

                                <!-- 钥匙交接 -->
                                <div class="form-item">
                                    <van-field label="钥匙交接" readonly :is-link="!isViewMode" input-align="right"
                                        label-width="200px"
                                        :model-value="getKeyHandoverStatusText(room.keyHandoverStatus)"
                                        placeholder="请选择状态"
                                        @click="!isViewMode && showKeyHandoverStatusPicker(roomIndex)" />
                                </div>
                                <div v-if="room.keyHandoverStatus === 2" class="penalty-row">
                                    <van-field v-model="room.keyPenalty" label="赔偿金额" type="number" input-align="right"
                                        placeholder="输入赔偿金额" :readonly="isViewMode"
                                        @update:model-value="onKeyPenaltyChange(roomIndex)">
                                        <template #button>
                                            <span class="unit">元从押金中扣除</span>
                                        </template>
                                    </van-field>
                                </div>

                                <!-- 清洁卫生 -->
                                <div class="form-item">
                                    <van-field label="清洁卫生" readonly :is-link="!isViewMode" input-align="right"
                                        label-width="100px" :model-value="getCleaningStatusText(room.cleaningStatus)"
                                        placeholder="请选择状态"
                                        @click="!isViewMode && showCleaningStatusPicker(roomIndex)" />
                                </div>
                                <div v-if="room.cleaningStatus === 2" class="penalty-row">
                                    <van-field v-model="room.cleaningPenalty" label="清理费用" type="number"
                                        input-align="right" placeholder="输入清理费用" :readonly="isViewMode"
                                        @update:model-value="onCleaningPenaltyChange(roomIndex)">
                                        <template #button>
                                            <span class="unit">元从押金中扣除</span>
                                        </template>
                                    </van-field>
                                </div>
                            </div>

                            <!-- 水电物业费情况 -->
                            <div class="utility-section">
                                <div class="section-title-blue">
                                    <div class="section-title-blue-text">
                                        <span class="tag"></span>
                                        水电物业费情况
                                    </div>
                                </div>

                                <!-- 抄表读数 -->
                                <div class="utility-row">
                                    <div class="utility-title">抄表读数</div>
                                    <div class="utility-fields">
                                        <van-field label="电表" placeholder="度数" input-align="right" label-width="120px"
                                            type="number" :readonly="isViewMode" v-model="room.elecMeterReading"
                                            @update:model-value="onUtilityReadingChange(roomIndex)">
                                            <template #button>
                                                <span class="unit">度</span>
                                            </template>
                                        </van-field>

                                        <van-field label="冷水表" placeholder="度数" input-align="right" label-width="120px"
                                            type="number" :readonly="isViewMode" v-model="room.coldWaterReading"
                                            @update:model-value="onUtilityReadingChange(roomIndex)">
                                            <template #button>
                                                <span class="unit">吨</span>
                                            </template>
                                        </van-field>

                                        <van-field label="热水表" placeholder="度数" input-align="right" label-width="120px"
                                            type="number" :readonly="isViewMode" v-model="room.hotWaterReading"
                                            @update:model-value="onUtilityReadingChange(roomIndex)">
                                            <template #button>
                                                <span class="unit">吨</span>
                                            </template>
                                        </van-field>
                                    </div>
                                </div>

                                <!-- 欠费金额 -->
                                <div class="utility-row">
                                    <div class="utility-title">欠费金额</div>
                                    <div class="utility-fields">
                                        <van-field label="电费" placeholder="金额" input-align="right" label-width="120px"
                                            type="number" :readonly="isViewMode" v-model="room.elecFee"
                                            @update:model-value="onUtilityFeeChange(roomIndex)">
                                            <template #button>
                                                <span class="unit">元</span>
                                            </template>
                                        </van-field>

                                        <van-field label="水费" placeholder="金额" input-align="right" label-width="120px"
                                            type="number" :readonly="isViewMode" v-model="room.waterFee"
                                            @update:model-value="onUtilityFeeChange(roomIndex)">
                                            <template #button>
                                                <span class="unit">元</span>
                                            </template>
                                        </van-field>

                                        <van-field label="物业费" placeholder="金额" input-align="right" label-width="120px"
                                            type="number" :readonly="isViewMode" v-model="room.pmFee"
                                            @update:model-value="onUtilityFeeChange(roomIndex)">
                                            <template #button>
                                                <span class="unit">元</span>
                                            </template>
                                        </van-field>
                                    </div>
                                </div>
                            </div>

                            <!-- 房间照片 -->
                            <div class="photos-section">
                                <div class="section-title-blue">
                                    <div class="section-title-blue-text">
                                        <span class="tag"></span>
                                        房间照片
                                    </div>
                                </div>
                                <van-uploader :model-value="getRoomPhotosArray(room)" :max-count="5"
                                    :after-read="(file: any) => onPhotoUpload(file, roomIndex)" :disabled="isViewMode"
                                    upload-text="上传照片"
                                    @update:model-value="(files: any[]) => updateRoomPhotos(files, roomIndex)" />
                            </div>

                            <!-- 固定资产、设备设施评估情况 (仅商铺或综合体显示) -->
                            <div v-if="shouldShowAssetsEvaluation(room)" class="evaluation-section">
                                <div class="section-title-blue">
                                    <div class="section-title-blue-text">
                                        <span class="tag"></span>
                                        固定资产、设备设施评估情况
                                    </div>
                                </div>
                                <van-field v-model="room.assetsSituation" rows="3" autosize type="textarea"
                                    :readonly="isViewMode" placeholder="请输入评估情况" />
                            </div>

                            <!-- 备注 -->
                            <div class="remark-section">
                                <div class="section-title-blue">
                                    <div class="section-title-blue-text">
                                        <span class="tag"></span>
                                        备注
                                    </div>
                                </div>
                                <van-field v-model="room.remark" rows="3" autosize type="textarea"
                                    :readonly="isViewMode" placeholder="请输入备注信息" />
                            </div>

                            <!-- 物业确认签字情况 -->
                            <div v-if="room.isFinanceConfirmed || room.isEngineeringConfirmed"
                                class="signature-section">
                                <div class="section-title-blue">
                                    <div class="section-title-blue-text">
                                        <span class="tag"></span>
                                        物业确认签字情况
                                    </div>
                                </div>

                                <div v-if="room.isFinanceConfirmed" class="signature-item">
                                    <div class="signature-title">
                                        综合或财务
                                        <van-tag type="primary" v-if="room.isFinanceConfirmed">已确认</van-tag>
                                        <van-tag type="default" v-else>未确认</van-tag>
                                    </div>

                                    <div class="signature-info">
                                        <!-- <span>{{ room.financeConfirmByName }}</span> -->
                                        <span>{{ formatDate(room.financeConfirmTime || '') }}</span>
                                        <div v-if="room.financeConfirmSignature" class="signature-image">
                                            <img :src="getSignatureUrl(room.financeConfirmSignature)" alt="财务确认签名" />
                                        </div>
                                    </div>
                                </div>

                                <div v-if="room.isEngineeringConfirmed" class="signature-item">
                                    <div class="signature-title">
                                        工程或客服
                                        <van-tag type="primary" v-if="room.isEngineeringConfirmed && room.isBusinessConfirmed">已确认</van-tag>
                                        <van-tag type="default" v-else>未确认</van-tag>
                                    </div>
                                    <div class="signature-info">
                                        <!-- <span>{{ room.engineeringConfirmByName }}</span> -->
                                        <span class="">{{ formatDate(room.engineeringConfirmTime || '') }}</span>
                                        <div v-if="room.engineeringConfirmSignature" class="signature-image">
                                            <img :src="getSignatureUrl(room.engineeringConfirmSignature)"
                                                alt="工程确认签名" />
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <!-- 保存 -->
                            <div class="save-section" v-if="!isViewMode">
                                <div class="form-actions">
                                    <van-button type="default" size="small" @click="onSave(roomIndex, false)"
                                        :loading="saving">暂存</van-button>
                                    <van-button type="primary" size="small" @click="onSave(roomIndex, true)"
                                        :loading="saving">确认</van-button>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 商服批量确认弹窗 -->
        <van-popup v-model:show="showBatchConfirmPopup" position="bottom" style="height: 60vh;">
            <div class="batch-confirm-popup">
                <div class="popup-header">
                    <van-button type="default" size="small" @click="onBatchConfirmCancel"
                        :disabled="batchConfirmLoading">取消</van-button>
                    <span class="popup-title">商服批量确认</span>
                    <van-button type="primary" size="small" @click="onBatchConfirmConfirm"
                        :loading="batchConfirmLoading">确认</van-button>
                </div>

                <div class="popup-content">
                    <!-- 出场日期选择 -->
                    <div class="batch-form-item">
                        <van-field label="出场日期" readonly is-link input-align="right" label-width="200px"
                            :model-value="formatDate(batchExitDate || '')" placeholder="请选择出场日期"
                            @click="showBatchDatePicker" required />
                    </div>

                    <!-- 选中的房源列表 -->
                    <div class="selected-rooms">
                        <div class="selected-title">已选房源（{{ selectedRoomsForBatch.length }}间）</div>
                        <div class="selected-list" v-if="selectedRoomsForBatch.length > 0">
                            <div v-for="room in selectedRoomsForBatch" :key="room.roomId" class="selected-room-item">
                                <img class="room-icon" src="../assets/images/house-icon.svg" alt="房源" />
                                <span class="room-name">{{ room.roomName }}</span>
                                <span class="room-status" v-if="room.isBusinessConfirmed">已确认</span>
                            </div>
                        </div>
                        <div v-else class="no-selected">
                            暂无选中房源
                        </div>
                    </div>
                </div>
            </div>
        </van-popup>

        <!-- 批量确认出场日期选择器弹窗 -->
        <van-popup v-model:show="showBatchDatePickerPopup" position="bottom">
            <van-date-picker v-model="tempBatchDate" title="选择出场日期" @confirm="onBatchDateConfirm"
                @cancel="onBatchDateCancel" v-if="showBatchDatePickerPopup" />
        </van-popup>

        <!-- 日期选择器弹窗 -->
        <van-popup v-model:show="showDatePickerPopup" position="bottom">
            <van-date-picker v-model="tempDate" title="选择出场日期" @confirm="onDateConfirm" @cancel="onDateCancel"
                v-if="showDatePickerPopup" />
        </van-popup>

        <!-- 租控管理选择器 -->
        <van-popup v-model:show="showRentControlPopup" position="bottom">
            <van-picker v-model="tempRentControl" title="选择租控管理" :columns="rentControlOptions"
                @confirm="onRentControlConfirm" @cancel="onRentControlCancel" v-if="showRentControlPopup" />
        </van-popup>

        <!-- 固定资产选择弹窗 -->
        <van-popup v-model:show="showAssetLibraryPopup" position="bottom" style="height: 80vh;">
            <div class="asset-library-popup">
                <div class="popup-header">
                    <van-button type="default" size="small" @click="onAssetLibraryCancel">取消</van-button>
                    <span class="popup-title">选择固定资产</span>
                    <van-button type="primary" size="small" @click="onAssetLibraryConfirm">确定</van-button>
                </div>

                <div class="popup-content">
                    <!-- 搜索区域 -->
                    <!-- <div class="search-section">
                        <van-field v-model="assetSearchForm.name" placeholder="请输入物品名称" />
                        <van-dropdown-menu>
                            <van-dropdown-item v-model="assetSearchForm.category" :options="assetCategoryOptions" title="种类" />
                        </van-dropdown-menu>
                        <van-button type="primary" @click="searchAssets">搜索</van-button>
                    </div> -->

                    <!-- 资产列表 -->
                    <div class="asset-list">
                        <van-loading v-if="assetLoading" type="spinner" color="#3583FF" />
                        <div v-else>
                            <van-checkbox-group v-model="selectedAssetIds">
                                <div v-for="asset in assetLibraryData" :key="asset.id" class="asset-library-item">
                                    <van-checkbox :name="asset.id">
                                        <div class="asset-item-content">
                                            <div class="asset-name">{{ asset.name }}</div>
                                            <div class="asset-category">{{ getAssetCategoryText(asset.category) }}</div>
                                            <div class="asset-spec">{{ asset.specification }}</div>
                                        </div>
                                    </van-checkbox>
                                </div>
                            </van-checkbox-group>
                        </div>
                    </div>
                </div>
            </div>
        </van-popup>

        <!-- 门窗墙体状态选择器 -->
        <van-popup v-model:show="showDoorWindowStatusPopup" position="bottom">
            <van-picker v-model="tempDoorWindowStatus" title="选择门窗墙体状态" :columns="doorWindowStatusOptions"
                @confirm="onDoorWindowStatusConfirm" @cancel="onDoorWindowStatusCancel"
                v-if="showDoorWindowStatusPopup" />
        </van-popup>

        <!-- 钥匙交接状态选择器 -->
        <van-popup v-model:show="showKeyHandoverStatusPopup" position="bottom">
            <van-picker v-model="tempKeyHandoverStatus" title="选择钥匙交接状态" :columns="keyHandoverStatusOptions"
                @confirm="onKeyHandoverStatusConfirm" @cancel="onKeyHandoverStatusCancel"
                v-if="showKeyHandoverStatusPopup" />
        </van-popup>

        <!-- 清洁卫生状态选择器 -->
        <van-popup v-model:show="showCleaningStatusPopup" position="bottom">
            <van-picker v-model="tempCleaningStatus" title="选择清洁卫生状态" :columns="cleaningStatusOptions"
                @confirm="onCleaningStatusConfirm" @cancel="onCleaningStatusCancel" v-if="showCleaningStatusPopup" />
        </van-popup>

        <!-- 批量设置出场日期弹窗 -->
        <van-popup v-model:show="showBatchExitDatePopup" position="bottom" style="height: 60vh;">
            <div class="batch-confirm-popup">
                <div class="popup-header">
                    <van-button type="default" size="small" @click="onBatchExitDateSetCancel"
                        :disabled="batchExitDateLoading">取消</van-button>
                    <span class="popup-title">批量设置出场日期</span>
                    <van-button type="primary" size="small" @click="onBatchExitDateSetConfirm"
                        :loading="batchExitDateLoading">确认</van-button>
                </div>

                <div class="popup-content">
                    <!-- 出场日期选择 -->
                    <div class="batch-form-item">
                        <van-field label="出场日期" readonly is-link input-align="right" label-width="200px"
                            :model-value="formatDate(batchExitDateForSet || '')" placeholder="请选择出场日期"
                            @click="showBatchExitDatePicker" required />
                    </div>

                    <!-- 选中的房源列表 -->
                    <div class="selected-rooms">
                        <div class="selected-title">已选房源（{{ selectedRoomsForBatch.length }}间）</div>
                        <div class="selected-list" v-if="selectedRoomsForBatch.length > 0">
                            <div v-for="room in selectedRoomsForBatch" :key="room.roomId" class="selected-room-item">
                                <img class="room-icon" src="../assets/images/house-icon.svg" alt="房源" />
                                <span class="room-name">{{ room.roomName }}</span>
                                <span class="room-status" v-if="room.isBusinessConfirmed">已确认</span>
                            </div>
                        </div>
                        <div v-else class="no-selected">
                            暂无选中房源
                        </div>
                    </div>
                </div>
            </div>
        </van-popup>

        <!-- 批量设置出场日期选择器弹窗 -->
        <van-popup v-model:show="showBatchExitDatePickerPopup" position="bottom">
            <van-date-picker v-model="tempBatchExitDateForSet" title="选择出场日期" @confirm="onBatchExitDateConfirm"
                @cancel="onBatchExitDateCancel" v-if="showBatchExitDatePickerPopup" />
        </van-popup>
        <!-- 收款码弹框 -->
        <van-popup v-model:show="showQRCodePopup" position="center" :close-on-click-overlay="true" round
            class="qr-code-popup">
            <div class="qr-code-content">
                <div class="qr-code-header">
                    <h3 class="qr-code-title">物业确认单</h3>
                    <van-icon name="cross" @click="closeQRCodePopup" class="close-icon" />
                </div>
                <!--                  
                 <div class="booking-info">
                     <div class="info-item">
                         <span class="label">项目名称：</span>
                         <span class="value">{{ currentBookingInfo.projectName }}</span>
                     </div>
                     <div class="info-item">
                         <span class="label">意向房源：</span>
                         <span class="value">{{ currentBookingInfo.roomName }}</span>
                     </div>
                     <div class="info-item">
                         <span class="label">客户名称：</span>
                         <span class="value">{{ currentBookingInfo.customerName }}</span>
                     </div>
                     <div class="info-item">
                         <span class="label">支付金额：</span>
                         <span class="value amount">¥{{ currentBookingInfo.unpaidAmount || currentBookingInfo.bookingAmount }}</span>
                     </div>
                 </div> -->

                <div class="qr-code-section">
                    <!-- <div v-if="qrCodeLoading" class="loading-section">
                         <van-loading size="24px" />
                         <span>生成收款码中...</span>
                     </div> -->
                    <div class="qr-code-wrapper">
                        <QRCode :value="qrCodeData" :size="200" />
                    </div>
                    <!-- <div v-else class="error-section">
                         <span>收款码生成失败</span>
                     </div> -->
                </div>
            </div>
        </van-popup>


        <!-- 操作按钮 -->
        <div class="action-buttons" v-if="exitMode === 'property-only' && !isViewMode">
            <!-- 进度提示 -->
            <!-- <div class="progress-tip" v-if="!allRoomsConfirmedForNext">
                <van-icon name="info-o" class="tip-icon" />
                <span class="tip-text">
                    请确保所有房源的商服、工程、财务都已确认后再进入下一步
                </span>
            </div> -->

            <div class="button-group">
                <van-button type="default" size="large" class="action-btn save-btn"
                    :disabled="!allRoomsConfirmedForNext" @click="handleNext">
                    进入费用结算
                </van-button>
                <van-button type="primary" size="large" class="action-btn" @click="handleChange">
                    物业交割并结算
                </van-button>
            </div>
        </div>

    </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineExpose, defineEmits, reactive, nextTick, computed } from 'vue';
import { showToast, showConfirmDialog } from 'vant';
import { saveExitRoom } from '../api/exit';
import type { ExitRoomAddDTO, ExitRoomAssetsAddDTO } from '../api/exit';
import QRCode from '@/components/QRCode.vue'

const props = defineProps({
    exitMode: {
        type: String,
        default: 'property-only'
    },
    exitId: {
        type: String,
        default: ''
    },
    exitSep: {
        type: Number,
        default: 1
    }
})
// 定义 emit 事件
const emit = defineEmits(['save', 'confirm', 'cancel', 'batchConfirm', 'copyConfirmUrl', 'batchSetExitDate', 'changeExitMode', 'next'])


// 资产状态选项
const assetStatusOptions = [
    { text: '完好', value: 1 },
    { text: '损坏', value: 2 },
    { text: '丢失', value: 3 }
];

// 租控管理选项
const rentControlOptions = [
    { text: '当前空置已做断电、锁门处理', value: 1 },
    { text: '二次出租无需断电处理,开门设置更新', value: 2 }
];

// 资产种类选项
const assetCategoryOptions = [
    { text: '全部', value: '' },
    { text: '家具', value: 1 },
    { text: '家电', value: 2 },
    { text: '办公用品', value: 3 },
    { text: '其他', value: 4 }
];

const roomList = ref<any[]>([])
const expandedRooms = ref<any[]>([])
const isViewMode = ref(false)

// 日期选择器相关
const showDatePickerPopup = ref(false)
const tempDate = ref<string[]>(['2024', '01', '01'])
const currentRoomIndex = ref(-1)

// 租控管理选择器相关
const showRentControlPopup = ref(false)
const tempRentControl = ref<number[]>([1])

// 固定资产库相关
const showAssetLibraryPopup = ref(false)
const assetLoading = ref(false)
const assetLibraryData = ref<any[]>([])
const selectedAssetIds = ref<string[]>([])
const assetSearchForm = reactive({
    name: '',
    category: '',
    pageNum: 1,
    pageSize: 20
})

// 商服批量确认相关
const showBatchConfirmPopup = ref(false)
const showBatchDatePickerPopup = ref(false)
const batchExitDate = ref<string>('')
const tempBatchDate = ref<string[]>(['2024', '01', '01'])
const batchConfirmLoading = ref(false)

// 批量设置出场日期相关
const showBatchExitDatePopup = ref(false)
const showBatchExitDatePickerPopup = ref(false)
const batchExitDateForSet = ref<string>('')
const tempBatchExitDateForSet = ref<string[]>(['2024', '01', '01'])
const batchExitDateLoading = ref(false)

const isAllSelected = ref(false)

const qrCodeData = ref('')
const showQRCodePopup = ref(false)

const closeQRCodePopup = () => {
    showQRCodePopup.value = false
}


const handleNext = () => {
    // 检查所有房间是否都确认通过
    if (!roomList.value || roomList.value.length === 0) {
        showToast('暂无房源数据')
        return
    }

    // 在property-only模式下，需要三方确认（商服、工程、财务）
    const unconfirmedRooms = roomList.value.filter(room =>
        !room.isBusinessConfirmed ||
        !room.isEngineeringConfirmed ||
        !room.isFinanceConfirmed
    )

    if (unconfirmedRooms.length > 0) {
        const unconfirmedRoomNames = unconfirmedRooms.map(room => {
            const missing = []
            if (!room.isBusinessConfirmed) missing.push('商服')
            if (!room.isEngineeringConfirmed) missing.push('工程')
            if (!room.isFinanceConfirmed) missing.push('财务')
            return `${room.roomName}(缺少${missing.join('、')}确认)`
        }).join('；')

        showToast({
            message: `以下房源还未完成确认：${unconfirmedRoomNames}`,
            type: 'fail',
            duration: 4000
        })
        return
    }

    // 所有房间都已确认，可以进入下一步（费用结算）
    // showToast({
    //     message: '物业交割完成，进入费用结算',
    //     type: 'success'
    // })

    emit('next', {
        confirmedRooms: roomList.value,
        mode: 'settlement'
    })
}
const handleChange = () => {
    // 检查所有房间是否都确认通过
    if (!roomList.value || roomList.value.length === 0) {
        showToast('暂无房源数据')
        return
    }

    // 在property-and-settlement模式下，只需要商服和工程确认（不需要财务确认）
    const unconfirmedRooms = roomList.value.filter(room =>
        !room.isBusinessConfirmed ||
        !room.isEngineeringConfirmed
    )

    if (unconfirmedRooms.length > 0) {
        const unconfirmedRoomNames = unconfirmedRooms.map(room => {
            const missing = []
            if (!room.isBusinessConfirmed) missing.push('商服')
            if (!room.isEngineeringConfirmed) missing.push('工程')
            return `${room.roomName}(缺少${missing.join('、')}确认)`
        }).join('；')

        showToast({
            message: `以下房源还未完成确认：${unconfirmedRoomNames}`,
            type: 'fail',
            duration: 4000
        })
        return
    }

    // 所有房间的商服和工程都已确认，可以切换到交割并结算模式
    showToast({
        message: '切换到物业交割并结算模式',
        type: 'success'
    })

    emit('changeExitMode', 'property-and-settlement')
}

// 计算选中的房源
const selectedRoomsForBatch = computed(() => {
    return roomList.value.filter(room => room.isSelected)
})

// 计算所有房间是否都已完成确认（用于下一步按钮）
const allRoomsConfirmedForNext = computed(() => {
    if (!roomList.value || roomList.value.length === 0) return false

    // 在property-only模式下，需要三方确认（商服、工程、财务）
    return roomList.value.every(room =>
        room.isBusinessConfirmed &&
        room.isEngineeringConfirmed &&
        room.isFinanceConfirmed
    )
})

// 计算所有房间是否都已完成商服和工程确认（用于切换模式按钮）
const allRoomsConfirmedForChange = computed(() => {
    if (!roomList.value || roomList.value.length === 0) return false

    // 在property-and-settlement模式下，只需要商服和工程确认
    return roomList.value.every(room =>
        room.isBusinessConfirmed &&
        room.isEngineeringConfirmed
    )
})

const onAllSelectChange = () => {
    // 使用nextTick确保DOM更新后再执行
    nextTick(() => {
        roomList.value.forEach(room => {
            room.isSelected = isAllSelected.value
        })
    })
}

const onRoomSelectChange = (roomIndex: number) => {
    // v-model会自动处理值的变化，这里只需要更新全选状态
    nextTick(() => {
        // 检查是否所有房源都被选中
        const allSelected = roomList.value.every(room => room.isSelected)
        const noneSelected = roomList.value.every(room => !room.isSelected)

        if (allSelected) {
            isAllSelected.value = true
        } else if (noneSelected) {
            isAllSelected.value = false
        } else {
            // 部分选中状态，根据需要可以设置为false或使用中间状态
            isAllSelected.value = false
        }
    })
}

const formatDate = (date: string) => {
    if (!date) return ''
    return date
}

const getRentControlText = (value: number) => {
    const option = rentControlOptions.find(opt => opt.value === value)
    return option ? option.text : ''
}

const getAssetCategoryText = (category: number) => {
    const categoryMap: Record<number, string> = {
        1: '家具',
        2: '家电',
        3: '办公用品',
        4: '其他'
    }
    return categoryMap[category] || '未知'
}

const shouldShowAssetsEvaluation = (room: any) => {
    // 根据房间类型判断是否显示评估情况
    // 商铺=31，综合体=32
    return room.propertyType === 31 || room.propertyType === 32
}

const getSignatureUrl = (signatureData: string) => {
    try {
        const data = JSON.parse(signatureData)
        return data.fileUrl || ''
    } catch (e) {
        return ''
    }
}

// 展开/收起房间
const toggleRoomExpand = (roomId: string) => {
    if (expandedRooms.value.includes(roomId)) {
        expandedRooms.value = expandedRooms.value.filter(id => id !== roomId)
    } else {
        expandedRooms.value.push(roomId)
    }
}

// 商服批量确认处理
const handleBatchBusinessConfirm = () => {
    console.log('handleBatchBusinessConfirm', roomList.value)
    const selectedRooms = roomList.value.filter(room => room.isSelected)
    if (selectedRooms.length === 0) {
        showToast('请先选择要确认的房源')
        return
    }

    // 重置批量确认数据
    batchExitDate.value = ''
    showBatchConfirmPopup.value = true
}

// 显示批量出场日期选择器
const showBatchDatePicker = () => {
    showBatchDatePickerPopup.value = true
}

// 批量日期选择确认
const onBatchDateConfirm = (date: any) => {
    showBatchDatePickerPopup.value = false
    const dateStr = date.selectedValues.join('-')
    batchExitDate.value = dateStr
}

// 批量日期选择取消
const onBatchDateCancel = () => {
    showBatchDatePickerPopup.value = false
}

// 批量设置出场日期处理
const handleBatchExitDate = () => {
    const selectedRooms = roomList.value.filter(room => room.isSelected)
    if (selectedRooms.length === 0) {
        showToast('请先选择要设置的房源')
        return
    }

    // 检查是否有已确认的房源
    const confirmedRooms = selectedRooms.filter(room => room.isBusinessConfirmed)
    if (confirmedRooms.length > 0) {
        showToast('存在商服已确认的房源，无法批量设置出场日期')
        return
    }

    // 重置批量设置出场日期数据
    batchExitDateForSet.value = ''
    showBatchExitDatePopup.value = true
}

// 显示批量设置出场日期选择器
const showBatchExitDatePicker = () => {
    showBatchExitDatePickerPopup.value = true
}

// 批量设置出场日期选择确认
const onBatchExitDateConfirm = (date: any) => {
    showBatchExitDatePickerPopup.value = false
    const dateStr = date.selectedValues.join('-')
    batchExitDateForSet.value = dateStr
}

// 批量设置出场日期选择取消
const onBatchExitDateCancel = () => {
    showBatchExitDatePickerPopup.value = false
}

// 批量设置出场日期确认
const onBatchExitDateSetConfirm = async () => {
    if (!batchExitDateForSet.value) {
        showToast('请选择出场日期')
        return
    }

    try {
        await showConfirmDialog({
            title: '确认提示',
            message: `确认为选中的${selectedRoomsForBatch.value.length}间房源批量设置出场日期吗？`,
            confirmButtonText: '确认',
            cancelButtonText: '取消'
        })
    } catch {
        return // 用户取消了操作
    }

    batchExitDateLoading.value = true

    try {
        // 批量更新选中房源的出场日期
        selectedRoomsForBatch.value.forEach(room => {
            room.exitDate = batchExitDateForSet.value
        })

        showToast({
            message: `成功为${selectedRoomsForBatch.value.length}间房源设置出场日期`,
            type: 'success'
        })

        showBatchExitDatePopup.value = false

        // 触发父组件事件
        emit('batchSetExitDate', {
            affectedRooms: selectedRoomsForBatch.value,
            exitDate: batchExitDateForSet.value
        })

    } catch (error: any) {
        console.error('批量设置出场日期失败:', error)

        let errorMessage = '批量设置出场日期失败，请重试'
        if (error?.response?.data?.msg) {
            errorMessage = error.response.data.msg
        } else if (error?.message) {
            errorMessage = error.message
        }

        showToast({
            message: errorMessage,
            type: 'fail'
        })
    } finally {
        batchExitDateLoading.value = false
    }
}

// 批量设置出场日期取消
const onBatchExitDateSetCancel = () => {
    showBatchExitDatePopup.value = false
}

// 批量确认确认
const onBatchConfirmConfirm = async () => {
    if (!batchExitDate.value) {
        showToast('请选择出场日期')
        return
    }

    try {
        await showConfirmDialog({
            title: '确认提示',
            message: `确认对选中的${selectedRoomsForBatch.value.length}间房源进行商服批量确认？确认后将无法修改。`,
            confirmButtonText: '确认',
            cancelButtonText: '取消'
        })
    } catch {
        return // 用户取消了操作
    }

    batchConfirmLoading.value = true

    try {
        const batchPromises = selectedRoomsForBatch.value.map(async (room) => {
            // 更新房间的出场日期
            room.exitDate = batchExitDate.value

            // 构造保存数据
            const saveData: ExitRoomAddDTO = {
                id: room.id,
                exitId: room.exitId,
                roomId: room.roomId,
                roomName: room.roomName,
                propertyType: room.propertyType,
                parcelName: room.parcelName,
                buildingName: room.buildingName,
                exitDate: room.exitDate,
                rentControl: room.rentControl || 1,
                doorWindowStatus: room.doorWindowStatus || 1,
                doorWindowPenalty: Number(room.doorWindowPenalty) || 0,
                keyHandoverStatus: room.keyHandoverStatus || 1,
                keyPenalty: Number(room.keyPenalty) || 0,
                cleaningStatus: room.cleaningStatus || 1,
                cleaningPenalty: Number(room.cleaningPenalty) || 0,
                elecMeterReading: Number(room.elecMeterReading) || 0,
                coldWaterReading: Number(room.coldWaterReading) || 0,
                hotWaterReading: Number(room.hotWaterReading) || 0,
                elecFee: Number(room.elecFee) || 0,
                waterFee: Number(room.waterFee) || 0,
                pmFee: Number(room.pmFee) || 0,
                roomPhotos: JSON.stringify(room.roomPhotos || []),
                assetsSituation: room.assetsSituation || '',
                remark: room.remark || '',
                isSubmit: true, // 批量确认直接提交
                isBusinessConfirmed: true,
                businessConfirmTime: new Date().toISOString(),
                businessConfirmBy: getCurrentUserId(),
                businessConfirmByName: getCurrentUserName(),
                exitRoomAssetsList: (room.assetList || []).map((asset: any) => ({
                    id: asset.id?.startsWith('temp_') ? undefined : asset.id,
                    exitId: room.exitId,
                    exitRoomId: room.id,
                    category: asset.category || 1,
                    name: asset.name || '',
                    specification: asset.specification || '',
                    count: asset.count || 1,
                    status: asset.status || 1,
                    penalty: Number(asset.penalty) || 0,
                    isAdd: asset.isAdd || false,
                    remark: asset.remark || '',
                    isDel: false
                })),
                isDel: false
            }

            return saveExitRoom(saveData)
        })

        // 批量执行所有保存操作
        await Promise.all(batchPromises)

        // 更新本地数据状态
        selectedRoomsForBatch.value.forEach(room => {
            room.isBusinessConfirmed = true
            room.businessConfirmTime = new Date().toISOString()
            room.businessConfirmBy = getCurrentUserId()
            room.businessConfirmByName = getCurrentUserName()
            room.isSelected = false // 取消选中状态
        })

        // 更新全选状态
        isAllSelected.value = false

        showToast({
            message: `成功确认${selectedRoomsForBatch.value.length}间房源`,
            type: 'success'
        })

        showBatchConfirmPopup.value = false

        // 触发父组件事件
        emit('batchConfirm', {
            confirmedRooms: selectedRoomsForBatch.value,
            exitDate: batchExitDate.value
        })

    } catch (error: any) {
        console.error('批量确认失败:', error)

        let errorMessage = '批量确认失败，请重试'
        if (error?.response?.data?.msg) {
            errorMessage = error.response.data.msg
        } else if (error?.message) {
            errorMessage = error.message
        }

        showToast({
            message: errorMessage,
            type: 'fail'
        })
    } finally {
        batchConfirmLoading.value = false
    }
}

// 批量确认取消
const onBatchConfirmCancel = () => {
    showBatchConfirmPopup.value = false
}

// 复制物业确认单地址
const handleCopyPropertyConfirmUrl = () => {
    showQRCodePopup.value = true
    // qrCodeData.value = ''
    const baseUrl = 'http://************:8571'
    // const orderId = formData.id || `temp_${Date.now()}`
    // const amount = formData.depositAmount
    // const customerName = encodeURIComponent(formData.customerName || '')

    // 构建支付链接 - 这里可以根据实际的支付系统来构建
    qrCodeData.value = `${baseUrl}/property-handover-detail?exitId=${props.exitId}`

    // 复制到剪贴板
    if (navigator.clipboard && window.isSecureContext) {
        // 现代浏览器的方式
        navigator.clipboard.writeText(qrCodeData.value).then(() => {
            showToast({
                message: `物业确认单地址已复制到剪贴板`,
                type: 'success'
            })
        }).catch(err => {
            console.error('复制失败:', err)
            fallbackCopyTextToClipboard(qrCodeData.value)
        })
    } else {
        // 兼容老浏览器的方式
        fallbackCopyTextToClipboard(qrCodeData.value)
    }


    // qrCodeData.value = paymentLink


    // const selectedRooms = roomList.value.filter(room => room.isSelected)
    // if (selectedRooms.length === 0) {
    //     showToast('请先选择要生成确认单的房源')
    //     return
    // }

    // try {
    //     // 生成物业确认单地址（这里应该根据实际业务逻辑生成）
    //     const roomIds = selectedRooms.map(room => room.roomId).join(',')
    //     // 这里应该是实际的物业确认单URL，可能需要调用后端API生成确认单链接
    //     const confirmUrl = `${window.location.origin}/property-confirm?rooms=${roomIds}&exitId=${selectedRooms[0]?.exitId || ''}`

    //     // 复制到剪贴板
    //     if (navigator.clipboard && window.isSecureContext) {
    //         // 现代浏览器的方式
    //         navigator.clipboard.writeText(confirmUrl).then(() => {
    //             showToast({
    //                 message: `物业确认单地址已复制到剪贴板（共${selectedRooms.length}间房源）`,
    //                 type: 'success'
    //             })
    //         }).catch(err => {
    //             console.error('复制失败:', err)
    //             fallbackCopyTextToClipboard(confirmUrl, selectedRooms.length)
    //         })
    //     } else {
    //         // 兼容老浏览器的方式
    //         fallbackCopyTextToClipboard(confirmUrl, selectedRooms.length)
    //     }

    //     // 触发父组件事件
    //     emit('copyConfirmUrl', {
    //         selectedRooms: selectedRooms,
    //         confirmUrl: confirmUrl
    //     })

    // } catch (error) {
    //     console.error('生成确认单地址失败:', error)
    //     showToast({
    //         message: '生成确认单地址失败，请重试',
    //         type: 'fail'
    //     })
    // }
}

// 兼容老浏览器的复制方法
const fallbackCopyTextToClipboard = (text: string) => {
    const textArea = document.createElement('textarea')
    textArea.value = text

    // 避免滚动到bottom
    textArea.style.top = '0'
    textArea.style.left = '0'
    textArea.style.position = 'fixed'

    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    try {
        const successful = document.execCommand('copy')
        if (successful) {
            showToast({
                message: `物业确认单地址已复制到剪贴板`,
                type: 'success'
            })
        } else {
            showToast({
                message: '复制失败，请手动复制',
                type: 'fail'
            })
        }
    } catch (err) {
        console.error('Fallback: 复制失败', err)
        showToast({
            message: '复制失败，请手动复制',
            type: 'fail'
        })
    }

    document.body.removeChild(textArea)
}

// 日期选择相关方法
const showDatePicker = (roomIndex: number) => {
    currentRoomIndex.value = roomIndex
    showDatePickerPopup.value = true
}

const onDateConfirm = (date: any) => {
    showDatePickerPopup.value = false
    const dateStr = date.selectedValues.join('-')
    roomList.value[currentRoomIndex.value].exitDate = dateStr
}

const onDateCancel = () => {
    showDatePickerPopup.value = false
}

// 租控管理相关方法
const showRentControlPicker = (roomIndex: any) => {
    currentRoomIndex.value = roomIndex
    const currentValue = roomList.value[roomIndex].rentControl || 1
    tempRentControl.value = [currentValue]
    showRentControlPopup.value = true
}

const onRentControlConfirm = (result: { selectedValues: number[]; selectedOptions: { text: string; value: number }[]; selectedIndexes: number[] }) => {
    console.log('onRentControlConfirm', result)
    showRentControlPopup.value = false
    roomList.value[currentRoomIndex.value].rentControl = result.selectedValues[0]
}

const onRentControlCancel = () => {
    showRentControlPopup.value = false
}

// 固定资产相关方法
const showAssetSelector = (roomIndex: number) => {
    currentRoomIndex.value = roomIndex
    selectedAssetIds.value = []
    showAssetLibraryPopup.value = true
    loadAssetLibrary()
}

const loadAssetLibrary = async () => {
    assetLoading.value = true
    try {
        // 这里需要调用实际的API
        // const res = await getFixedAssetsList(assetSearchForm)
        // assetLibraryData.value = res.data || []

        // 模拟数据
        assetLibraryData.value = [
            { id: '1', name: '空调', category: 2, specification: '1.5匹' },
            { id: '2', name: '办公桌', category: 1, specification: '1.2m*0.6m' },
            { id: '3', name: '办公椅', category: 1, specification: '可调节高度' }
        ]
    } catch (error) {
        console.error('加载固定资产库失败:', error)
    } finally {
        assetLoading.value = false
    }
}

const searchAssets = () => {
    loadAssetLibrary()
}

const onAssetLibraryConfirm = () => {
    if (selectedAssetIds.value.length === 0) {
        // 可以显示提示
        return
    }

    const room = roomList.value[currentRoomIndex.value]
    if (!room.assetList) {
        room.assetList = []
    }

    selectedAssetIds.value.forEach(assetId => {
        const asset = assetLibraryData.value.find(a => a.id === assetId)
        if (asset) {
            room.assetList.push({
                id: `temp_${Date.now()}_${Math.random()}`,
                name: asset.name,
                category: asset.category,
                specification: asset.specification,
                count: 1,
                status: 1,
                penalty: 0,
                remark: '',
                isAdd: true
            })
        }
    })

    showAssetLibraryPopup.value = false
}

const onAssetLibraryCancel = () => {
    showAssetLibraryPopup.value = false
}

const removeAsset = (roomIndex: number, assetIndex: number) => {
    roomList.value[roomIndex].assetList.splice(assetIndex, 1)
}

// 各种状态变化处理方法
const onAssetStatusChange = (roomIndex: number, assetIndex: number) => {
    const asset = roomList.value[roomIndex].assetList[assetIndex]
    if (asset.status === 1) {
        asset.penalty = 0
        asset.remark = ''
    }
}

const onAssetPenaltyChange = (roomIndex: number, assetIndex: number) => {
    // 可以添加数据同步逻辑
}

const onDoorWindowStatusChange = (roomIndex: number) => {
    const room = roomList.value[roomIndex]
    if (room.doorWindowStatus === 1) {
        room.doorWindowPenalty = 0
    }
}

const onDoorWindowPenaltyChange = (roomIndex: number) => {
    // 可以添加数据同步逻辑
}

const onKeyHandoverStatusChange = (roomIndex: number) => {
    const room = roomList.value[roomIndex]
    if (room.keyHandoverStatus === 1) {
        room.keyPenalty = 0
    }
}

const onKeyPenaltyChange = (roomIndex: number) => {
    // 可以添加数据同步逻辑
}

const onCleaningStatusChange = (roomIndex: number) => {
    const room = roomList.value[roomIndex]
    if (room.cleaningStatus === 1) {
        room.cleaningPenalty = 0
    }
}

const onCleaningPenaltyChange = (roomIndex: number) => {
    // 可以添加数据同步逻辑
}

const onUtilityReadingChange = (roomIndex: number) => {
    // 可以添加数据同步逻辑
}

const onUtilityFeeChange = (roomIndex: number) => {
    // 可以添加数据同步逻辑
}

// 获取房间照片数组格式
const getRoomPhotosArray = (room: any) => {
    if (!room.roomPhotos) return []

    if (typeof room.roomPhotos === 'string') {
        try {
            const parsed = JSON.parse(room.roomPhotos)
            return Array.isArray(parsed) ? parsed : []
        } catch (e) {
            console.warn('Invalid JSON format for roomPhotos:', room.roomPhotos)
            return []
        }
    }

    return Array.isArray(room.roomPhotos) ? room.roomPhotos : []
}

// 更新房间照片
const updateRoomPhotos = (files: any[], roomIndex: number) => {
    const room = roomList.value[roomIndex]
    if (room) {
        room.roomPhotos = files
    }
}

// 照片上传处理
const onPhotoUpload = (file: any, roomIndex: number) => {
    console.log('照片上传:', file, roomIndex)
    // 这里可以添加实际的上传逻辑
    // 上传成功后可以将文件信息添加到 roomPhotos 中
}

// 获取当前用户ID
const getCurrentUserId = (): string => {
    // 从localStorage或其他地方获取用户ID
    // 这里可以根据项目的实际用户管理方式调整
    return localStorage.getItem('userId') || 'system'
}

// 获取当前用户名称
const getCurrentUserName = (): string => {
    // 从localStorage或其他地方获取用户名
    // 这里可以根据项目的实际用户管理方式调整
    return localStorage.getItem('userName') || '系统用户'
}

// 验证房间数据
const validateRoomData = (room: any): { isValid: boolean; message: string } => {
    if (!room.exitDate) {
        return { isValid: false, message: '请选择出场日期' }
    }

    if (!room.rentControl) {
        return { isValid: false, message: '请选择租控管理方式' }
    }

    // 检查房屋其他情况的必填项
    if (room.doorWindowStatus === 2 && (!room.doorWindowPenalty || room.doorWindowPenalty <= 0)) {
        return { isValid: false, message: '门窗损坏时请输入赔偿金额' }
    }

    if (room.keyHandoverStatus === 2 && (!room.keyPenalty || room.keyPenalty <= 0)) {
        return { isValid: false, message: '钥匙未交齐时请输入赔偿金额' }
    }

    if (room.cleaningStatus === 2 && (!room.cleaningPenalty || room.cleaningPenalty <= 0)) {
        return { isValid: false, message: '保洁收费时请输入费用金额' }
    }

    // 检查配套资产的赔偿金
    if (room.assetList && room.assetList.length > 0) {
        for (let asset of room.assetList) {
            if ((asset.status === 2 || asset.status === 3) && (!asset.penalty || asset.penalty <= 0)) {
                return { isValid: false, message: `配套"${asset.name}"损坏或丢失时请输入赔偿金额` }
            }
        }
    }

    return { isValid: true, message: '' }
}

// 保存相关状态
const saving = ref(false)

// 保存方法
const onSave = async (roomIndex: number, isSubmit: boolean) => {
    // 验证当前房间数据
    const currentRoom = roomList.value[roomIndex]
    if (!currentRoom) {
        showToast('当前房间数据不存在')
        return
    }

    // 验证房间数据完整性
    const validation = validateRoomData(currentRoom)
    if (!validation.isValid) {
        showToast(validation.message)
        return
    }

    // 如果是确认操作，显示确认对话框
    if (isSubmit) {
        try {
            await showConfirmDialog({
                title: '确认提示',
                message: '确认后将无法修改，是否继续？',
                confirmButtonText: '确认',
                cancelButtonText: '取消'
            })
        } catch {
            return // 用户取消了操作
        }
    }

    saving.value = true

    try {
        // 转换资产列表为API格式
        const exitRoomAssetsList: ExitRoomAssetsAddDTO[] = (currentRoom.assetList || []).map((asset: any) => ({
            id: asset.id?.startsWith('temp_') ? undefined : asset.id, // 临时ID不传给后端
            exitId: currentRoom.exitId,
            exitRoomId: currentRoom.id,
            category: asset.category || 1,
            name: asset.name || '',
            specification: asset.specification || '',
            count: asset.count || 1,
            status: asset.status || 1,
            penalty: Number(asset.penalty) || 0,
            isAdd: asset.isAdd || false,
            remark: asset.remark || '',
            isDel: false
        }))

        // 构造保存数据，符合ExitRoomAddDTO格式
        const saveData: ExitRoomAddDTO = {
            id: currentRoom.id,
            exitId: currentRoom.exitId,
            roomId: currentRoom.roomId,
            roomName: currentRoom.roomName,
            propertyType: currentRoom.propertyType,
            parcelName: currentRoom.parcelName,
            buildingName: currentRoom.buildingName,
            exitDate: currentRoom.exitDate,
            rentControl: currentRoom.rentControl || 1,
            doorWindowStatus: currentRoom.doorWindowStatus || 1,
            doorWindowPenalty: Number(currentRoom.doorWindowPenalty) || 0,
            keyHandoverStatus: currentRoom.keyHandoverStatus || 1,
            keyPenalty: Number(currentRoom.keyPenalty) || 0,
            cleaningStatus: currentRoom.cleaningStatus || 1,
            cleaningPenalty: Number(currentRoom.cleaningPenalty) || 0,
            elecMeterReading: Number(currentRoom.elecMeterReading) || 0,
            coldWaterReading: Number(currentRoom.coldWaterReading) || 0,
            hotWaterReading: Number(currentRoom.hotWaterReading) || 0,
            elecFee: Number(currentRoom.elecFee) || 0,
            waterFee: Number(currentRoom.waterFee) || 0,
            pmFee: Number(currentRoom.pmFee) || 0,
            // 将数组转换回 JSON 字符串
            roomPhotos: JSON.stringify(currentRoom.roomPhotos),
            assetsSituation: currentRoom.assetsSituation || '',
            remark: currentRoom.remark || '',
            isSubmit: isSubmit,
            // 如果是确认操作，设置商服确认相关字段
            isBusinessConfirmed: isSubmit,
            businessConfirmTime: isSubmit ? new Date().toISOString() : '',
            // 从localStorage获取用户信息，这与其他组件的做法一致
            businessConfirmBy: isSubmit ? getCurrentUserId() : '',
            businessConfirmByName: isSubmit ? getCurrentUserName() : '',
            exitRoomAssetsList: exitRoomAssetsList,
            isDel: false
        }

        console.log('保存数据:', saveData)

        // 调用保存接口
        const response = await saveExitRoom(saveData)
        console.log('保存响应:', response)

        // 更新本地数据
        Object.assign(currentRoom, {
            ...currentRoom,
            isSubmit: isSubmit,
            isBusinessConfirmed: isSubmit,
            businessConfirmTime: isSubmit ? new Date().toISOString() : currentRoom.businessConfirmTime,
            businessConfirmBy: isSubmit ? getCurrentUserId() : currentRoom.businessConfirmBy,
            businessConfirmByName: isSubmit ? getCurrentUserName() : currentRoom.businessConfirmByName
        })

        // 显示成功提示
        showToast({
            message: isSubmit ? '确认成功' : '暂存成功',
            type: 'success'
        })

        // 触发父组件事件
        emit('save', { roomData: saveData, roomIndex, isSubmit, response })

        if (isSubmit) {
            emit('confirm', { roomData: saveData, roomIndex, response })
        }

    } catch (error: any) {
        console.error('保存失败:', error)

        // 根据错误类型显示不同的提示信息
        let errorMessage = '保存失败，请重试'
        if (error?.response?.data?.msg) {
            errorMessage = error.response.data.msg
        } else if (error?.message) {
            errorMessage = error.message
        }

        showToast({
            message: errorMessage,
            type: 'fail'
        })
    } finally {
        saving.value = false
    }
}

// 暴露方法给父组件
defineExpose({
    open(exitData?: any) {
        if (exitData && exitData.exitRoomList) {
            roomList.value = exitData.exitRoomList.map((room: any) => ({
                ...room,
                // 设置默认值
                rentControl: room.rentControl || 1,
                doorWindowStatus: room.doorWindowStatus || 1,
                doorWindowPenalty: room.doorWindowPenalty || 0,
                keyHandoverStatus: room.keyHandoverStatus || 1,
                keyPenalty: room.keyPenalty || 0,
                cleaningStatus: room.cleaningStatus || 1,
                cleaningPenalty: room.cleaningPenalty || 0,
                elecMeterReading: room.elecMeterReading || 0,
                coldWaterReading: room.coldWaterReading || 0,
                hotWaterReading: room.hotWaterReading || 0,
                elecFee: room.elecFee || 0,
                waterFee: room.waterFee || 0,
                pmFee: room.pmFee || 0,
                // 处理 roomPhotos JSON 字符串
                roomPhotos: parseRoomPhotos(room.roomPhotos),
                assetsSituation: room.assetsSituation || '',
                remark: room.remark || '',
                assetList: room.assetList || [],
                isSelected: false
            }))
        }
    },
})

// 解析房间照片数据
const parseRoomPhotos = (photosData: any) => {
    if (!photosData) return []

    if (typeof photosData === 'string') {
        try {
            const parsed = JSON.parse(photosData)
            return Array.isArray(parsed) ? parsed : []
        } catch (e) {
            console.warn('Invalid JSON format for roomPhotos:', photosData)
            return []
        }
    }

    return Array.isArray(photosData) ? photosData : []
}



// 门窗墙体状态选项
const doorWindowStatusOptions = [
    { text: '完好', value: 1 },
    { text: '损坏', value: 2 }
];

// 钥匙交接状态选项
const keyHandoverStatusOptions = [
    { text: '已交齐', value: 1 },
    { text: '未交齐', value: 2 }
];

// 清洁卫生状态选项
const cleaningStatusOptions = [
    { text: '自行打扫完毕、洁净', value: 1 },
    { text: '保洁及垃圾清理收费', value: 2 }
];

// 门窗墙体状态选择器相关
const showDoorWindowStatusPopup = ref(false)
const tempDoorWindowStatus = ref<number[]>([1])

// 钥匙交接状态选择器相关
const showKeyHandoverStatusPopup = ref(false)
const tempKeyHandoverStatus = ref<number[]>([1])

// 清洁卫生状态选择器相关
const showCleaningStatusPopup = ref(false)
const tempCleaningStatus = ref<number[]>([1])

const getDoorWindowStatusText = (value: number) => {
    const option = doorWindowStatusOptions.find(opt => opt.value === value)
    return option ? option.text : ''
}

const getKeyHandoverStatusText = (value: number) => {
    const option = keyHandoverStatusOptions.find(opt => opt.value === value)
    return option ? option.text : ''
}

const getCleaningStatusText = (value: number) => {
    const option = cleaningStatusOptions.find(opt => opt.value === value)
    return option ? option.text : ''
}

// 门窗墙体状态相关方法
const showDoorWindowStatusPicker = (roomIndex: number) => {
    currentRoomIndex.value = roomIndex
    const currentValue = roomList.value[roomIndex].doorWindowStatus || 1
    tempDoorWindowStatus.value = [currentValue]
    showDoorWindowStatusPopup.value = true
}

const onDoorWindowStatusConfirm = (result: { selectedValues: number[]; selectedOptions: { text: string; value: number }[]; selectedIndexes: number[] }) => {
    showDoorWindowStatusPopup.value = false
    const newValue = result.selectedValues[0]
    roomList.value[currentRoomIndex.value].doorWindowStatus = newValue
    onDoorWindowStatusChange(currentRoomIndex.value)
}

const onDoorWindowStatusCancel = () => {
    showDoorWindowStatusPopup.value = false
}

// 钥匙交接状态相关方法
const showKeyHandoverStatusPicker = (roomIndex: number) => {
    currentRoomIndex.value = roomIndex
    const currentValue = roomList.value[roomIndex].keyHandoverStatus || 1
    tempKeyHandoverStatus.value = [currentValue]
    showKeyHandoverStatusPopup.value = true
}

const onKeyHandoverStatusConfirm = (result: { selectedValues: number[]; selectedOptions: { text: string; value: number }[]; selectedIndexes: number[] }) => {
    showKeyHandoverStatusPopup.value = false
    const newValue = result.selectedValues[0]
    roomList.value[currentRoomIndex.value].keyHandoverStatus = newValue
    onKeyHandoverStatusChange(currentRoomIndex.value)
}

const onKeyHandoverStatusCancel = () => {
    showKeyHandoverStatusPopup.value = false
}

// 清洁卫生状态相关方法
const showCleaningStatusPicker = (roomIndex: number) => {
    currentRoomIndex.value = roomIndex
    const currentValue = roomList.value[roomIndex].cleaningStatus || 1
    tempCleaningStatus.value = [currentValue]
    showCleaningStatusPopup.value = true
}

const onCleaningStatusConfirm = (result: { selectedValues: number[]; selectedOptions: { text: string; value: number }[]; selectedIndexes: number[] }) => {
    showCleaningStatusPopup.value = false
    const newValue = result.selectedValues[0]
    roomList.value[currentRoomIndex.value].cleaningStatus = newValue
    onCleaningStatusChange(currentRoomIndex.value)
}

const onCleaningStatusCancel = () => {
    showCleaningStatusPopup.value = false
}
</script>

<style scoped lang="less">
/* 基础样式保持不变 */
.rooms-section {
    box-sizing: border-box;
    padding: 32px 32px 0 32px;
    border-radius: 30px;
    position: relative;
    top: -20px;
    background-color: #f0f0f0;
}
.wyjg-container-bottom {
    padding-bottom: 160px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 32px;
    font-weight: 600;

    .section-title-right {
        display: flex;
        align-items: center;
        gap: 10px;

        .section-title-right-item {
            display: flex;
            align-items: center;

            .section-title-right-item-tag {
                width: 20px;
                height: 20px;
                background-color: #1677FF;
                border-radius: 2px;
                margin-right: 10px;
            }

            .section-title-right-item-text {
                font-size: 24px;
                color: #333;
            }
        }
    }
}

.title-icon {
    width: 40px;
    height: 40px;
}

.section-title-text {
    display: flex;
    align-items: center;
    margin-bottom: -10px;
}

.room-count {
    color: #3583FF;
}

.room-list-content {
    // padding: 0 32px;
    background: #fff;
    margin-top: 20px;
    border-radius: 20px;

    .all-select {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;

        .all-select-left {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 28px;
            color: #333;

            .van-checkbox {
                padding: 0 !important;
            }

            :deep(.van-checkbox__icon) {
                font-size: 34px;
            }
        }

        .all-select-btn {
            display: flex;
            align-items: center;
            gap: 10px;
        }
    }
}

.room-list {
    margin-top: 16px;

    :deep(.van-checkbox__icon) {
        font-size: 34px;
    }
}

.room-card {
    background-color: #FFFFFF;
    border-radius: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

.room-header {
    display: flex;
    align-items: center;
    padding: 16px;
    cursor: pointer;
    border-bottom: 1px solid #F5F5F5;

    .room-header-left {
        display: flex;
        align-items: flex-start;
        height: 70px;

        :deep(.van-checkbox__icon) {
            font-size: 34px;
        }
    }
}

.room-info {
    flex: 1;
}

.room-name-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.room-name-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    width: 40px;
    height: 40px;
    border-radius: 6px;
    background-color: #acacac;
    color: #fff;
    font-size: 24px;
    font-weight: 500;
}

.room-name-tag-on {
    background-color: #3583FF;
    color: #fff;
}

.house-icon {
    width: 30px;
    height: 30px;
    margin-right: 16px;
}

.room-name {
    font-size: 28px;
    font-weight: 500;
    color: #242433;
}

.room-date {
    font-size: 24px;
    color: #919199;
}

.expand-icon {
    color: #C8C9CC;
    font-size: 16px;
}

.room-content {
    padding: 0 16px 16px;
}

.form-row {
    margin-bottom: 16px;
}

/* 蓝色标题样式 */
.section-title-blue {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28px;
    font-weight: 500;
    padding: 22px 0 22px 16px;
    border-bottom: 1px solid #E8EBFF;
    color: #000;
}

.section-title-blue-text {
    display: flex;
    align-items: center;
    gap: 10px;
}

.tag {
    width: 10px;
    height: 30px;
    background-color: #1677FF;
    border-radius: 8px;
    margin-right: 10px;
}

.add-btn {
    height: 60px !important;
    font-size: 28px !important;
    font-weight: normal !important;
    padding: 0 12px !important;
}

.empty-assets {
    text-align: center;
    color: #C8C9CC;
    font-size: 24px;
    padding: 20px 0;
}

/* 资产项目样式 */
.asset-item {
    padding: 16px 0;
    border-bottom: 1px solid #F5F5F5;
}

.asset-item:last-child {
    border-bottom: none;
}

.asset-info {
    width: 100%;
}

.asset-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.asset-label-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.delete-icon {
    color: #FF4D4F;
    font-size: 30px;
    cursor: pointer;
}

.asset-name {
    font-size: 28px;
    color: #242433;
    line-height: 1.4em;
}

.asset-status-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-label {
    font-size: 24px;
    color: #666;
}

.asset-details {
    margin-top: 12px;
}

/* 房屋其他情况样式 */
.other-section {
    margin-bottom: 16px;
}

.form-item {
    margin-bottom: 8px;
}

.penalty-row {
    padding: 0 16px 16px;
}

/* 水电物业费样式 */
.utility-section {
    margin-bottom: 16px;
}

.utility-row {
    margin-bottom: 20px;
}

.utility-title {
    font-size: 26px;
    font-weight: 500;
    color: #242433;
    padding: 12px 16px;
    background: #F8F9FA;
    border-radius: 8px;
    margin-bottom: 12px;
}

.utility-fields {
    padding: 0 16px;
}

/* 照片区域样式 */
.photos-section {
    margin-bottom: 16px;

}

/* 评估区域样式 */
.evaluation-section {
    margin-bottom: 16px;
}

/* 备注区域样式 */
.remark-section {
    margin-bottom: 16px;
}

/* 签字区域样式 */
.signature-section {
    margin-bottom: 16px;
}

.signature-item {
    padding: 16px;
    background: #F8F9FA;
    border-radius: 8px;
    margin-bottom: 12px;
    .signature-title {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 28px;
    }
}

.signature-info {
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    font-size: 24px;
    color: #999;
}

.signature-image img {
    width: 220px;
    height: auto;
    // object-fit: contain;
}

/* 单位文字样式 */
.unit {
    color: #919199;
    font-size: 28px;
}

/* 固定资产库弹窗样式 */
.asset-library-popup {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
}

.batch-confirm-popup {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #E8EBFF;
}

.popup-title {
    font-size: 32px;
    font-weight: 600;
    color: #242433;
}

.popup-content {
    flex: 1;
    overflow-y: auto;
    padding: 40px;
}

.batch-form-item {
    margin-bottom: 32px;
}

.selected-rooms {
    margin-top: 32px;
}

.selected-title {
    font-size: 56px;
    font-weight: 500;
    color: #242433;
    padding: 24px 0;
    border-bottom: 2px solid #F5F5F5;
}

.selected-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 24px 0;
}

.selected-room-item {
    display: flex;
    align-items: center;
    gap: 24px;
    font-size: 52px;
    color: #333;
    padding: 24px 0;
    border-bottom: 2px solid #F5F5F5;
}

.selected-room-item:last-child {
    border-bottom: none;
}

.room-icon {
    width: 48px;
    height: 48px;
    flex-shrink: 0;
}

.room-status {
    margin-left: auto;
    font-size: 44px;
    color: #3583FF;
    background-color: #E8F3FF;
    padding: 8px 16px;
    border-radius: 24px;
}

.no-selected {
    text-align: center;
    color: #C8C9CC;
    font-size: 48px;
    padding: 80px 0;
}

.search-section {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

.asset-list {
    height: 100%;
    overflow-y: auto;
}

.asset-library-item {
    padding: 16px 0;
    border-bottom: 1px solid #F5F5F5;
}

.asset-item-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.asset-name {
    font-size: 28px;
    color: #242433;
    font-weight: 500;
}

.asset-category {
    font-size: 24px;
    color: #666;
}

.asset-spec {
    font-size: 24px;
    color: #999;
}

/* 深度选择器样式 */
:deep(.van-checkbox__label) {
    flex: 1;
}

:deep(.van-checkbox) {
    align-items: center;
    padding: 0 16px;
}

:deep(.van-checkbox__icon) {
    height: 36px;
    width: 36px;
    border-radius: 36px !important;
}

:deep(.van-checkbox__icon--checked) {
    background-color: #3583FF;
    border-color: #3583FF;
}

/* :deep(.van-button--primary) {
    height: 88px;
    line-height: 88px;
    font-size: 32px;
} */

:deep(.van-popup) {
    max-height: 85vh;
}

:deep(.van-field__label) {
    font-size: 26px;
}

:deep(.van-field__body) {
    font-size: 26px;
}

:deep(.van-dropdown-menu__item) {
    font-size: 26px;
}

:deep(.van-dropdown-menu__bar) {
    height: 60px;
    font-size: 24px;
}

/* 保存区域样式 */
.save-section {
    padding: 24px 16px;
    background-color: #FFFFFF;
    border-top: 1px solid #E8EBFF;
    // margin-top: 20px;
    position: sticky;
    bottom: 0;
    z-index: 10;

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 32px;
    }

    .form-actions .van-button {
        // flex: 1;
        // height: 80px;
        width: 160px;
        height: 60px;
        font-size: 32px;
        /* font-weight: 500; */
        border-radius: 30px;
        border: none;
        font-weight: normal !important;
    }

    .form-actions .van-button--default {
        background-color: #fff;
        color: #3583FF;
        border: 1px solid #3583FF;
    }

    .form-actions .van-button--primary {
        background-color: #3583FF;
        color: #FFFFFF;
    }

    .form-actions .van-button--default:active {
        background-color: #E0E0E0;
    }

    .form-actions .van-button--primary:active {
        background-color: #3583FF;
    }

    .form-actions .van-button--loading {
        opacity: 0.7;
    }
}






.action-buttons {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 32px 32px 32px 32px;
    gap: 24px;
    width: 100%;
    background: #fff;
    position: fixed;
    bottom: 0;
    z-index: 10;

    .progress-tip {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 24px;
        background-color: #FFF7E6;
        border: 2px solid #FFD591;
        border-radius: 16px;

        .tip-icon {
            color: #FA8C16;
            font-size: 32px;
            flex-shrink: 0;
        }

        .tip-text {
            color: #D46B08;
            font-size: 26px;
            line-height: 1.4;
        }
    }

    .button-group {
        display: flex;
        align-items: center;
        gap: 32px;
    }

    .van-button {
        flex: 1;
        height: 88px;
        border-radius: 44px;
        font-weight: 500;
        font-size: 32px;
        transition: all 0.3s ease;
    }

    .save-btn {
        color: #fff;
        border: 2px solid #3583FF;
        background: #3583FF;
    }

    .save-btn:active {
        background: #f0f7ff;
    }

    .van-button--primary {
        background: #23B7FF;
        border-color: #23B7FF;
    }

    .van-button--primary:active {
        background: #23B7FF;
    }

    .van-button--disabled {
        opacity: 0.4;
        cursor: not-allowed;
        background-color: #F5F5F5 !important;
        color: #C8C9CC !important;
        border-color: #E4E7ED !important;
    }
}

/* 收款码弹框样式 */
.qr-code-popup {
    :deep(.van-popup) {
        border-radius: 20px;
        overflow: hidden;
    }

    .qr-code-content {
        padding: 40px;
        width: 600px;
        max-width: 90vw;
        background-color: #fff;
    }

    .qr-code-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;

        .qr-code-title {
            font-size: 36px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .close-icon {
            font-size: 40px;
            color: #999;
            cursor: pointer;
        }
    }

    .booking-info {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 12px;

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            font-size: 28px;

            &:last-child {
                margin-bottom: 0;
            }

            .label {
                color: #666;
            }

            .value {
                color: #333;
                font-weight: 500;

                &.amount {
                    color: #ff4444;
                    font-size: 32px;
                    font-weight: bold;
                }
            }
        }
    }

    .qr-code-section {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 30px;
        min-height: 200px;

        .loading-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
            color: #666;
            font-size: 28px;
        }

        .qr-code-wrapper {
            display: flex;
            justify-content: center;
            padding: 20px;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .error-section {
            color: #ff4444;
            font-size: 28px;
        }
    }

    .qr-code-actions {
        display: flex;
        justify-content: center;

        .action-btn {
            min-width: 200px;
            height: 80px;
            font-size: 30px;
        }
    }
}
</style>