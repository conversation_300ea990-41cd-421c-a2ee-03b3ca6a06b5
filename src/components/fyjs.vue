<!-- 费用结算 -->
<template>
    <div class="settlement-container">
        <div class="settlement-section">
            <!-- 费用结算标题和操作按钮 -->
            <div class="section-header">
                <h3 class="section-title">
                    <span class="section-tag"></span>
                    费用结算
                </h3>
                <van-button v-if="!readonly" type="primary" size="small" @click="handleAddFeeItem" icon="plus">
                    添加
                </van-button>
            </div>

            <!-- 费用列表 -->
            <div class="fee-list">
                <div v-for="(item, index) in feeItems" :key="item.id" class="fee-item">
                    <div class="fee-item-header">
                        <div class="fee-type">
                            <van-tag :type="item.payType === 1 ? 'success' : 'danger'" size="medium">
                                {{ item.payType === 1 ? '收' : '支' }}
                            </van-tag>
                            <span class="fee-name">{{ item.subjectName || '请选择费用类型' }}</span>
                        </div>
                        <div class="fee-amount">{{ item.amount || 0 }}</div>
                        <van-icon v-if="item.type === 2 && !readonly" name="delete-o" color="#ee0a24" size="36"
                            @click="removeFeeItem(item)" />
                    </div>

                    <div class="fee-period" v-if="item.startDate || item.endDate">
                        {{ formatDateRange(item.startDate, item.endDate) }}
                    </div>

                    <!-- 编辑表单 -->
                    <van-cell-group v-if="item.type === 2 && !readonly" class="edit-form">
                        <van-field :value="item.payType === 1 ? '收' : '支'" label="收支类型" placeholder="请选择收支类型" readonly
                            is-link @click="showPayTypePicker(item)" />
                        <van-field v-model="item.subjectName" label="费用类型" placeholder="请选择费用类型" readonly is-link
                            @click="showFeeTypePicker(item)" />
                        <van-field v-model="item.amount" type="number" label="金额" placeholder="请输入金额"
                            suffix-icon="clear" @click-suffix-icon="item.amount = 0" />
                        <van-field v-model="item.startDate" label="开始日期" placeholder="请选择开始日期" readonly is-link
                            @click="showDatePicker(item, 'start')" />
                        <van-field v-model="item.endDate" label="结束日期" placeholder="请选择结束日期" readonly is-link
                            @click="showDatePicker(item, 'end')" />
                        <van-field v-model="item.remark" label="费用说明" placeholder="请输入费用说明" />
                    </van-cell-group>
                </div>
            </div>

            <!-- 是否减免 -->
            <van-cell-group class="discount-section">
                <van-cell center>
                    <template #title>
                        <span>是否减免</span>
                    </template>
                    <template #right-icon>
                        <van-switch v-model="formData.isDiscount" :disabled="readonly" size="22" />
                    </template>
                </van-cell>

                <template v-if="formData.isDiscount">
                    <van-field v-model="formData.discountAmount" type="number" label="减免金额" placeholder="请输入" input-align="right"
                        suffix-text="元" @input="handleDiscountAmountChange" />
                    <van-field v-model="formData.discountReason" label="减免原因" placeholder="请输入" input-align="right"/>
                </template>
            </van-cell-group>

            <!-- 最终应退 -->
            <div class="final-amount-section">
                <div class="final-amount">
                    <span class="label">最终应退</span>
                    <!-- <span class="amount">{{ Math.abs(finalFeeAmount) }} 元</span> -->
                    <span class="amount">{{ finalFeeAmount }} 元</span>

                </div>
            </div>

            <!-- 退款处理方式 -->
            <van-cell-group class="refund-section" v-if="finalFeeAmount < 0">
                <van-field v-model="refundProcessTypeText" label="退款处理方式" placeholder="请选择" readonly is-link label-width="180px" input-align="right"
                    @click="showRefundTypePicker" />
            </van-cell-group>

            <!-- 收款信息 -->
            <van-cell-group class="payee-section" v-if="finalFeeAmount < 0">
                <div class="section-title-small">
                    <span class="section-tag"></span>
                    收款信息
                </div>
                <van-field v-model="formData.payeeName" label="收款人" placeholder="请输入" required input-align="right"/>
                <van-field v-model="formData.payeeAccount" label="收款账号" placeholder="请输入" required input-align="right"/>
                <van-field v-model="formData.bankName" label="开户银行" placeholder="请输入" required input-align="right"/>
            </van-cell-group>

            <!-- 手续办理情况 -->
            <van-cell-group class="procedure-section">
                <div class="section-title-small">
                    <span class="section-tag"></span>
                    手续办理情况
                </div>
                <van-field v-model="licenseStatusText" label="营业执照" placeholder="请选择" readonly is-link input-align="right"
                    @click="showLicenseStatusPicker" />
                <van-field v-model="taxCertStatusText" label="税务登记证" placeholder="请选择" readonly is-link input-align="right"
                    @click="showTaxCertStatusPicker" />
            </van-cell-group>

            <!-- 底部选择器 -->
            <div class="bottom-actions">
                <van-radio-group v-model="submitType" direction="horizontal">
                    <van-radio name="save">只结算，暂不退款</van-radio>
                    <van-radio name="submit">结算并申请退款</van-radio>
                </van-radio-group>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <van-button 
                    type="default" 
                    size="large" 
                    class="action-btn save-btn" 
                    :loading="saving"
                    :disabled="saving || submitting"
                    @click="handleSaveOnly"
                >
                    {{ saving ? '保存中...' : '保存' }}
                </van-button>
                <van-button 
                    type="primary" 
                    size="large" 
                    class="action-btn"
                    :loading="submitting"
                    :disabled="saving || submitting"
                    @click="handleSubmitSettlement"
                >
                    {{ submitting ? '提交中...' : '提交结算单' }}
                </van-button>
            </div>



            <!-- Picker 弹窗 -->
            <van-popup v-model:show="showPicker" position="bottom">
                <van-picker :columns="pickerColumns" @confirm="onPickerConfirm" @cancel="showPicker = false" />
            </van-popup>

            <!-- 日期选择器 -->
            <van-popup v-model:show="showDatePickerPopup" position="bottom">
                <van-datetime-picker v-model="currentDate" type="date" @confirm="onDateConfirm"
                    @cancel="showDatePickerPopup = false" />
            </van-popup>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineProps, defineEmits, defineExpose } from 'vue';
import { showToast, showConfirmDialog } from 'vant';


// 定义接口类型
interface ExitCost {
    id: string
    exitId: string
    costId: string
    startDate: string
    endDate: string
    payType: number
    subjectId: string
    subjectName: string
    receivableDate: string
    amount: number
    remark: string
    type: number // 0-接口返回的系统费用，1-系统计算生成的费用，2-用户手动添加的费用
    createByName: string
    updateByName: string
    isDel: boolean
}

interface ExitAddDTO {
    id: string
    projectId: string
    contractId: string
    contractNo: string
    contractUnionId: string
    terminateId: string
    refundId: string
    customerId: string
    customerName: string
    processType: number
    progressStatus: number
    isDiscount: boolean
    discountAmount: number
    discountReason: string
    finalAmount: number
    refundProcessType: number
    payeeName: string
    payeeAccount: string
    bankName: string
    licenseStatus: number
    taxCertStatus: number
    refundApplyType: number
    signType: number
    signAttachments: string
    exitCostList: ExitCost[]
    isSubmit: boolean
}

// Props定义
const props = defineProps<{
    exitInfo?: any
    costList?: ExitCost[]
    exitRoomList?: any[]
    readonly?: boolean
    contractTerminateInfo?: any
}>()

// 定义emits
const emit = defineEmits(['save', 'updatePenaltyAmount'])

// 表单数据
const formData = reactive<ExitAddDTO>({
    id: '',
    projectId: '',
    contractId: '',
    contractNo: '',
    contractUnionId: '',
    terminateId: '',
    refundId: '',
    customerId: '',
    customerName: '',
    processType: 1,
    progressStatus: 1,
    isDiscount: false,
    discountAmount: 0,
    discountReason: '',
    finalAmount: 0,
    refundProcessType: 1,
    payeeName: '',
    payeeAccount: '',
    bankName: '',
    licenseStatus: 1,
    taxCertStatus: 1,
    refundApplyType: 1,
    signType: 1,
    signAttachments: '',
    exitCostList: [],
    isSubmit: false
})

// 费用项列表
const feeItems = ref<ExitCost[]>([


])

// 费用科目列表
const feeSubjects = ref([
    { text: '定金', value: '10' },
    { text: '保证金', value: '20' },
    { text: '租金', value: '30' },
    { text: '罚没金', value: '40' },
    { text: '代收水电', value: '50' }
])

// Picker 相关
const showPicker = ref(false)
const showDatePickerPopup = ref(false)
const currentDate = ref(new Date())
const pickerColumns = ref<any[]>([])
const currentEditItem = ref<ExitCost | null>(null)
const currentPickerType = ref('')
const currentDateType = ref('')

// 提交类型
const submitType = ref('save')

// 加载状态
const saving = ref(false)
const submitting = ref(false)

// 计算总费用
const totalFeeAmount = computed(() => {
    const total = feeItems.value.reduce((sum, item) => {
        const amount = parseFloat(String(item.amount || 0))
        if (item.payType === 1) { // 收
            return sum + amount
        } else { // 支
            return sum - amount
        }
    }, 0)
    return Math.round(total * 100) / 100
})

// 最终费用金额
const finalFeeAmount = computed(() => {
    let amount = totalFeeAmount.value
    if (formData.isDiscount && formData.discountAmount) {
        const discountAmount = parseFloat(String(formData.discountAmount || 0))
        amount += discountAmount // 减免金额是减少支出，所以是加
    }
    return Math.round(amount * 100) / 100
})

// 退款处理方式文本
const refundProcessTypeText = computed(() => {
    const options = ['', '退款', '暂存客户账户']
    return options[formData.refundProcessType] || '退款'
})

// 营业执照状态文本
const licenseStatusText = computed(() => {
    const options = ['', '未办理执照', '需注销', '已注销']
    return options[formData.licenseStatus] || '未办理执照'
})

// 税务登记证状态文本
const taxCertStatusText = computed(() => {
    const options = ['', '未办理执照', '需注销', '已注销']
    return options[formData.taxCertStatus] || '未办理执照'
})

// 添加费项
const handleAddFeeItem = () => {
    const newFeeItem: ExitCost = {
        id: `temp_${Date.now()}`,
        exitId: formData.id,
        costId: '',
        startDate: '',
        endDate: '',
        payType: 2, // 默认为支出
        subjectId: '',
        subjectName: '',
        receivableDate: '',
        amount: 0,
        remark: '',
        type: 2, // 手动添加
        createByName: '',
        updateByName: '',
        isDel: false
    }
    feeItems.value.push(newFeeItem)
    formData.exitCostList = [...feeItems.value]
}

// 移除费项
const removeFeeItem = (feeItem: ExitCost) => {
    const index = feeItems.value.findIndex(item => item.id === feeItem.id)
    if (index !== -1) {
        feeItems.value.splice(index, 1)
        formData.exitCostList = [...feeItems.value]
    }
}

// 格式化日期范围显示
const formatDateRange = (startDate?: string, endDate?: string) => {
    if (!startDate && !endDate) {
        return ''
    }
    if (startDate && endDate) {
        return `${startDate}~${endDate}`
    }
    if (startDate) {
        return `${startDate}~`
    }
    if (endDate) {
        return `~${endDate}`
    }
    return ''
}

// 显示收支类型选择器
const showPayTypePicker = (item: ExitCost) => {
    currentEditItem.value = item
    currentPickerType.value = 'payType'
    pickerColumns.value = [
        { text: '收', value: 1 },
        { text: '支', value: 2 }
    ]
    showPicker.value = true
}

// 显示费用类型选择器
const showFeeTypePicker = (item: ExitCost) => {
    currentEditItem.value = item
    currentPickerType.value = 'feeType'
    pickerColumns.value = feeSubjects.value
    showPicker.value = true
}

// 显示日期选择器
const showDatePicker = (item: ExitCost, type: 'start' | 'end') => {
    currentEditItem.value = item
    currentDateType.value = type
    const dateValue = type === 'start' ? item.startDate : item.endDate
    currentDate.value = dateValue ? new Date(dateValue) : new Date()
    showDatePickerPopup.value = true
}

// 显示退款类型选择器
const showRefundTypePicker = () => {
    currentPickerType.value = 'refundType'
    pickerColumns.value = [
        { text: '退款', value: 1 },
        { text: '暂存客户账户', value: 2 }
    ]
    showPicker.value = true
}

// 显示营业执照状态选择器
const showLicenseStatusPicker = () => {
    currentPickerType.value = 'licenseStatus'
    pickerColumns.value = [
        { text: '未办理执照', value: 1 },
        { text: '需注销', value: 2 },
        { text: '已注销', value: 3 }
    ]
    showPicker.value = true
}

// 显示税务登记证状态选择器
const showTaxCertStatusPicker = () => {
    currentPickerType.value = 'taxCertStatus'
    pickerColumns.value = [
        { text: '未办理执照', value: 1 },
        { text: '需注销', value: 2 },
        { text: '已注销', value: 3 }
    ]
    showPicker.value = true
}

// Picker 确认
const onPickerConfirm = ({ selectedOptions }: any) => {
    const selectedOption = selectedOptions[0]

    if (currentPickerType.value === 'payType' && currentEditItem.value) {
        currentEditItem.value.payType = selectedOption.value
    } else if (currentPickerType.value === 'feeType' && currentEditItem.value) {
        currentEditItem.value.subjectId = selectedOption.value
        currentEditItem.value.subjectName = selectedOption.text
    } else if (currentPickerType.value === 'refundType') {
        formData.refundProcessType = selectedOption.value
    } else if (currentPickerType.value === 'licenseStatus') {
        formData.licenseStatus = selectedOption.value
    } else if (currentPickerType.value === 'taxCertStatus') {
        formData.taxCertStatus = selectedOption.value
    }

    showPicker.value = false
}

// 日期确认
const onDateConfirm = (value: Date) => {
    if (currentEditItem.value) {
        const dateStr = value.toISOString().split('T')[0]
        if (currentDateType.value === 'start') {
            currentEditItem.value.startDate = dateStr
        } else {
            currentEditItem.value.endDate = dateStr
        }
    }
    showDatePickerPopup.value = false
}

// 处理减免金额变更
const handleDiscountAmountChange = (value: string) => {
    const numValue = parseFloat(value)
    if (numValue > Math.abs(totalFeeAmount.value)) {
        // Toast.fail('减免金额不能大于应退金额')
        formData.discountAmount = Math.abs(totalFeeAmount.value)
    }
}

// 保存方法
const handleSave = async (isSubmit: boolean) => {
    try {
        // 验证必填字段
        if (finalFeeAmount.value < 0) {
            if (!formData.payeeName || !formData.payeeAccount || !formData.bankName) {
                showToast('应退款项，请填写完整的承租方收款信息')
                return false
            }
        }

        // 验证手动添加的费用项
        const invalidFeeItems = feeItems.value.filter(item => 
            item.type === 2 && (!item.subjectName || !item.amount)
        )
        if (invalidFeeItems.length > 0) {
            showToast('请完善费用类型和金额信息')
            return false
        }

        // 确保所有费用项的金额都是数字类型
        feeItems.value.forEach(item => {
            if (typeof item.amount === 'string') {
                item.amount = parseFloat(item.amount) || 0
            }
            // 保留2位小数
            item.amount = Math.round((item.amount || 0) * 100) / 100
        })

        // 更新最终金额
        formData.finalAmount = finalFeeAmount.value
        formData.isSubmit = isSubmit
        formData.exitCostList = [...feeItems.value]

        // 构造保存数据
        const saveData = {
            ...formData,
            // 根据当前退款申请方式设置
            refundApplyType: submitType.value === 'submit' ? 2 : 1
        }

        // 发送保存事件到父组件
        emit('save', saveData)
        
        // 返回成功状态，让调用方处理后续逻辑
        return true

    } catch (error) {
        console.error('保存失败:', error)
        showToast('保存失败，请重试')
        return false
    }
}


// 仅保存方法
const handleSaveOnly = async () => {
    saving.value = true
    try {
        const result = await handleSave(false)
        if (result) {
            // 保存成功后可以选择是否关闭弹窗或进行其他操作
        }
    } finally {
        saving.value = false
    }
}

// 提交结算单方法
const handleSubmitSettlement = async () => {
    submitting.value = true
    try {
        const result = await handleSave(true)
        if (result) {
            // 提交成功后的处理
        }
    } finally {
        submitting.value = false
    }
}

// 获取表单数据
const getFormData = () => {
    // 确保所有费用项的金额都是数字类型
    feeItems.value.forEach(item => {
        if (typeof item.amount === 'string') {
            item.amount = parseFloat(item.amount) || 0
        }
        // 保留2位小数
        item.amount = Math.round((item.amount || 0) * 100) / 100
    })

    // 更新最终金额和费用列表
    const currentFormData = { ...formData }
    currentFormData.finalAmount = finalFeeAmount.value
    currentFormData.exitCostList = [...feeItems.value]

    return currentFormData
}

// 初始化费用列表
const initCostList = () => {
    if (props.costList) {
        feeItems.value = [...props.costList]
        formData.exitCostList = [...feeItems.value]
    }
}

// 暴露给父组件的方法
defineExpose({
    open: (exitData: any) => {
        console.log('费用结算组件 open 方法被调用:', exitData)
        
        if (exitData?.exitCostList && Array.isArray(exitData.exitCostList)) {
            feeItems.value = [...exitData.exitCostList]
            formData.exitCostList = [...exitData.exitCostList]
        }
        
        // 如果有 exitInfo，更新表单数据
        if (exitData?.exitInfo) {
            Object.assign(formData, exitData.exitInfo)
        }
        
        // 如果直接传入费用列表
        if (Array.isArray(exitData)) {
            feeItems.value = [...exitData]
            formData.exitCostList = [...exitData]
        }
    },
    handleSave,
    getFormData,
    // 新增方法供外部调用
    handleSaveOnly,
    handleSubmitSettlement
})

// 初始化
initCostList()
</script>

<style scoped>
.settlement-container {
    /* background: #f7f8fa; */
    /* min-height: 100vh; */
    /* padding-bottom: 200px; */
    box-sizing: border-box;
    padding: 32px 32px 32px 32px;
    .settlement-section {
        /* background: #f7f8fa; */
        /* min-height: 100vh; */
        /* padding: 32px; */
        background: #fff;
        border-radius: 16px;
        box-sizing: border-box;
        padding-bottom: 32px;
    }
}


.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32px 32px 24px;
    background: #fff;
    margin-bottom: 20px;
    border-radius: 16px 16px 0 0;
}

.section-title {
    font-size: 36px;
    font-weight: 600;
    color: #323233;
    margin: 0;
    display: flex;
    align-items: center;
}

.fee-list {
    margin-bottom: 20px;
}

.fee-item {
    background: #fff;
    margin-bottom: 20px;
    padding: 32px;
}

.fee-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.fee-type {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.fee-name {
    font-size: 28px;
    color: #323233;
    font-weight: 500;
}

.fee-amount {
    font-size: 28px;
    color: #323233;
    font-weight: 600;
    margin-right: 16px;
}

.fee-period {
    font-size: 24px;
    color: #969799;
    margin-bottom: 16px;
}

.edit-form {
    margin-top: 16px;
}

.discount-section {
    margin-bottom: 20px;
}

.final-amount-section {
    background: #fff;
    padding: 32px;
    margin-bottom: 20px;
}

.final-amount {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.final-amount .label {
    font-size: 32px;
    color: #323233;
}

.final-amount .amount {
    font-size: 36px;
    color: #ff6034;
    font-weight: 600;
}

.refund-section,
.payee-section,
.procedure-section {
    margin-bottom: 20px;
}

.section-title-small {
    font-size: 28px;
    color: #323233;
    font-weight: 500;
    padding: 24px 32px 16px;
    background: #fff;
    /* border-bottom: 2px solid #ebedf0; */
    display: flex;
    align-items: center;
}

.action-buttons {

    /* position: fixed;
    bottom: 100px;
    left: 32px;
    right: 32px; */
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 32px 32px 0 32px;
    gap: 32px;
    /* z-index: 100; */
    .van-button {
        height: 80px;
        border-radius: 40px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    .save-btn {
        color: #3583FF;
        border: 1px solid #3583FF;
        background: #fff;
    }
    .save-btn:active {
        background: #f0f7ff;
    }
    .van-button--primary {
        background: #3583FF;
        border-color: #3583FF;
    }
    .van-button--primary:active {
        background: #2968c7;
    }
    .van-button--disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

.action-btn {
    flex: 1;
    height: 88px;
    font-size: 32px;
}

.bottom-actions {
    /* position: fixed;
    bottom: 0;
    left: 0;
    right: 0; */
    /* background: #fff; */
    padding: 32px;
    /* border-top: 2px solid #ebedf0; */
    /* z-index: 100; */
}

.bottom-actions .van-radio-group {
    display: flex;
    justify-content: space-between;
}

.bottom-actions .van-radio {
    flex: 1;
    justify-content: center;
}

/* Vant 组件样式调整 */
:deep(.van-cell) {
    font-size: 28px;
    padding: 24px 32px;
}

:deep(.van-field__label) {
    font-size: 28px;
    color: #323233;
    width: 160px;
}

:deep(.van-field__control) {
    font-size: 28px;
    color: #323233;
}

:deep(.van-tag) {
    font-size: 24px;
    padding: 4px 16px;
}

:deep(.van-switch) {
    transform: scale(1.2);
}

:deep(.van-button--large) {
    height: 88px;
    font-size: 32px;
}

:deep(.van-button--small) {
    height: 56px;
    font-size: 24px;
    padding: 0 24px;
}

:deep(.van-radio) {
    font-size: 28px;
}

:deep(.van-radio__label) {
    color: #323233;
}

/* 适配不同屏幕尺寸 */
@media (max-width: 750px) {
    .section-header {
        padding: 24px 24px 16px;
    }

    .section-title {
        font-size: 32px;
    }

    .fee-item {
        padding: 24px;
    }

    .action-buttons {
        left: 24px;
        right: 24px;
        bottom: 80px;
    }

    .bottom-actions {
        padding: 24px;
    }
}

.section-tag {
    display: inline-block;
    width: 8px;
    height: 30px;
    background: #007AFF;
    border-radius: 10px;
    margin-right: 10px;
}
</style>